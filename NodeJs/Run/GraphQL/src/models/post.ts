import {
    GjirafaLivestreamStatus,
    GjirafaQualityTypeStatus,
    PostDocumentType,
    PostPreviewGjirafaType,
    PostState,
} from '../generated/resolvers-types'
import { CategoryModel } from './user'

export type PostModel = {
    id: string
    userId: string
    title?: string | null
    text?: string | null
    textHtml?: string | null
    textDelta?: string | null
    publishedAt: string | null
    pinnedAt?: string | null
    state: PostState
    assets: PostAssetModel[]
    previewAssets: PreviewAssetModel[]
    categories: CategoryModel[]
    fullAssets?: boolean | null
    counts: PostCounts
    price?: number | null
    savedPostInfo?: {
        id: string
        savedAt: string
    } | null
    isAgeRestricted: boolean
    isSponsored: boolean
    isExcludedFromRss: boolean | undefined
    myVote: number
    pollId?: string | null
    voteScore: number
    hasPreview: boolean
    hasPreviewInternal: boolean
    communityId?: string | null
}

export type CommentModel = {
    id: string
    userId: string
    text?: string | null
    textHtml?: string | null
    textDelta?: string | null
    publishedAt: string
    state: PostState
    assets: PostAssetModel[]
    counts: PostCounts
    siblingId?: string | null
    parentId: string | null
    parent?: ParentModel | null
    post?: PostModel | null
    myVote: number
    voteScore: number
}

export type ParentModel = PostParentModel | CommentParentModel

export type PostParentModel = PostModel & { type: 'post' }

export type CommentParentModel = CommentModel & { type: 'comment' }

export type SavedPostModel = {
    id: string
    savedAt: string
    post: PostModel
}

export type PostCounts = {
    comments: number
    replies: number
}

export type PollModel = {
    id: string
    deadline: string
    options: PollOptionModel[]
}

export type PollOptionModel = {
    id: string
    title: string
    voteCount: number
    hasVotedFor: boolean
}

export type PreviewAssetModel = PreviewGjirafaModel | PreviewImageModel | PreviewDocumentModel | PreviewGjirafaLiveModel

export type PreviewDocumentModel = {
    type: PostDocumentType
    name?: string
    thumbnailUrl?: string
    assetType: 'preview-document'
}

export type PreviewImageModel = {
    url: string
    width: number
    height: number
    assetType: 'preview-image'
}

export type PreviewGjirafaLiveModel = {
    thumbnailUrl?: string
    assetType: 'preview-gjirafa-live'
}

export type PreviewGjirafaModel = {
    previewStaticUrl: string
    previewAnimatedUrl?: string | null
    previewStripUrl?: string | null
    duration: number
    type: PostPreviewGjirafaType
    thumbnailUrl?: string
    width: number
    height: number
    assetType: 'preview-gjirafa'
}

export type PostAssetModel =
    | PostDocumentAsset
    | PostImageAsset
    | PostGjirafaAsset
    | PostGjirafaLivestreamAsset
    | PostYoutubeAsset
    | PostBunnyAsset
    | PostEmptyAsset

export type PostYoutubeAsset = {
    id: string
    thumbnailUrl?: string | null
    thumbnail?: Image | null
    height?: number | null
    width?: number | null
    previewUrl?: string | null
    assetType: 'youtube'
}

export type PostBunnyAsset = {
    url: string
    assetType: 'bunny'
}

export type PostEmptyAsset = {
    dummy?: string
    assetType: 'empty'
}

export type PostImageAsset = {
    url: string
    width: number
    height: number
    assetType: 'image'
}

export type Image = {
    url: string
    width: number
    height: number
}

export type PostDocumentAsset = {
    url: string
    type: PostDocumentType
    name: string
    thumbnailUrl?: string | null
    thumbnail?: Image | null
    assetType: 'document'
}

export type PostGjirafaLivestreamAsset = {
    id: string
    playbackUrl: string
    channelPublicId: string
    assetType: 'gjirafa-livestream'
    status: GjirafaLivestreamStatus
    thumbnailUrl?: string | null
    thumbnail?: Image | null
}

export type PostGjirafaAsset = {
    progressTillReadiness?: number
    progressTillCompleteness?: number
    width: number
    height: number
    key?: string | null
    keyId: string
    videoStreamUrl?: string | null
    audioStreamUrl?: string | null
    audioStaticUrl?: string | null
    hasAudio: boolean
    hasVideo: boolean
    id: string
    hidden: boolean
    duration: number
    audioByteSize?: number | null
    status: GjirafaQualityTypeStatus
    thumbnailUrl?: string | null
    thumbnail?: Image | null
    previewAnimatedUrl?: string | null
    previewStaticUrl: string
    previewStripUrl?: string | null
    assetType: 'gjirafa'
    isLivestreamRecording: boolean
    timestamp?: number | null
}
