/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { PostInput } from './PostInput';
export type CreatePostRequest = {
    publishedAt?: string | null;
    categories: Array<string>;
    attributes: PostInput;
    isAgeRestricted: boolean;
    isSponsored: boolean;
    communityId?: string | null;
    hasPreview: boolean;
};

