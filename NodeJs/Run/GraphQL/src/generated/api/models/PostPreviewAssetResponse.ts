/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ImageAssetDto } from './ImageAssetDto';
import type { PreviewDocumentResponse } from './PreviewDocumentResponse';
import type { PreviewGjirafaLivestreamResponse } from './PreviewGjirafaLivestreamResponse';
import type { PreviewGjirafaResponse } from './PreviewGjirafaResponse';
import type { PreviewImageResponse } from './PreviewImageResponse';
export type PostPreviewAssetResponse = {
    image?: PreviewImageResponse;
    gjirafa?: PreviewGjirafaResponse;
    document?: PreviewDocumentResponse;
    gjirafaLive?: PreviewGjirafaLivestreamResponse;
    thumbnailImage?: ImageAssetDto;
};

