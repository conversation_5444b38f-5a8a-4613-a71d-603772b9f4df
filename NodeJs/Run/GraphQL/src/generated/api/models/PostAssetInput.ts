/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { DocumentAsset } from './DocumentAsset';
import type { GjirafaAssetInput } from './GjirafaAssetInput';
import type { GjirafaLivestreamAssetInput } from './GjirafaLivestreamAssetInput';
import type { ImageAssetInput } from './ImageAssetInput';
import type { YouTubeAsset } from './YouTubeAsset';
export type PostAssetInput = {
    image?: ImageAssetInput;
    gjirafa?: GjirafaAssetInput;
    gjirafaLivestream?: GjirafaLivestreamAssetInput;
    document?: DocumentAsset;
    thumbnail?: string | null;
    thumbnailImage?: ImageAssetInput;
    youtube?: YouTubeAsset;
};

