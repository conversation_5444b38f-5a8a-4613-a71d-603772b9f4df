/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { PostInput } from './PostInput';
export type UpdatePostRequest = {
    publishedAt?: string | null;
    pinnedAt?: string | null;
    categories: Array<string>;
    attributes: PostInput;
    isAgeRestricted: boolean;
    isSponsored: boolean;
    excludeFromRss?: boolean | null;
    hasPreview: boolean;
};

