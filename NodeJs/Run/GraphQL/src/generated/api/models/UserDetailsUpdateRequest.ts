/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { UpdateUserProfileImage } from './UpdateUserProfileImage';
export type UserDetailsUpdateRequest = {
    path: string;
    bio: string;
    name: string;
    isOfAge?: boolean | null;
    hasPostPreviews?: boolean | null;
    hasWelcomeMessageEnabled?: boolean | null;
    profileImage?: UpdateUserProfileImage;
    emailPublic?: string | null;
    emailInvoice?: string | null;
};

