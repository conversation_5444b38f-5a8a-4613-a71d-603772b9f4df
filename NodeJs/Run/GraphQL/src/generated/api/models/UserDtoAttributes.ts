/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Analytics } from './Analytics';
import type { DiscordDtoAttributes } from './DiscordDtoAttributes';
import type { GjirafaLivestreamMeta } from './GjirafaLivestreamMeta';
import type { ImageAsset } from './ImageAsset';
import type { NotificationsEnabled } from './NotificationsEnabled';
import type { SupportCounts } from './SupportCounts';
import type { UserCompany } from './UserCompany';
import type { UserStatus } from './UserStatus';
export type UserDtoAttributes = {
    name?: string | null;
    bio?: string | null;
    createdAt?: string | null;
    image?: ImageAsset;
    path?: string | null;
    pathChangeableAt?: string | null;
    subscribable: boolean;
    verified: boolean;
    stripeAccountId?: string | null;
    creatorSuspended?: boolean | null;
    counts: SupportCounts;
    status: UserStatus;
    hasRssFeed: boolean;
    hasDrm: boolean;
    hasSpotifyExport: boolean;
    hasSpotifyConnection: boolean;
    hasLivestreams: boolean;
    hasGiftsAllowed: boolean;
    spotifyUri?: string | null;
    spotifyFeedReady: boolean;
    language?: string | null;
    discord?: DiscordDtoAttributes;
    notificationsEnabled?: NotificationsEnabled;
    analytics: Analytics;
    company?: UserCompany;
    privacyPolicyEffectiveAt?: string | null;
    lastChargeFailedAt?: string | null;
    gjirafaLivestream?: GjirafaLivestreamMeta;
    isOfAge?: boolean | null;
    hasPostPreviews: boolean;
};

