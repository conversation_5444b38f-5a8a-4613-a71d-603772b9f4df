import { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';
import { CommunityModel } from '../models/community';
import { NotificationModel } from '../models/notification';
import { ExpectedIncomeModel } from '../models/statistics';
import { PollModel, PostModel, CommentModel, ParentModel, PreviewAssetModel, PostAssetModel } from '../models/post';
import { UserModel, UserDetailsModel, LivestreamDetailsModel } from '../models/user';
import { MessageThreadModel, MessageThreadDetailsModel, MessageModel, MessageAssetModel } from '../models/message-thread';
import { SubscriptionModel, FullSubscriptionModel, SubscribeRequestModel } from '../models/subscription';
import { DataSourceContext } from '../context';
export type Maybe<T> = T | null;
export type InputMaybe<T> = T | undefined | null;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  BigInt: { input: any; output: any; }
  DateTime: { input: Date | string; output: Date | string; }
  Void: { input: any; output: any; }
};

export type CategoriesOrderInput = {
  categoryIds: Array<Scalars['String']['input']>;
};

export type Category = {
  __typename?: 'Category';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  slug: Scalars['String']['output'];
};

export type CategoryCreateInput = {
  index?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
};

export type CategoryPayload = {
  __typename?: 'CategoryPayload';
  category: Category;
};

export type CategoryUpdateInput = {
  name: Scalars['String']['input'];
};

export type Comment = {
  __typename?: 'Comment';
  assets: Array<PostAsset>;
  counts: PostCounts;
  id: Scalars['ID']['output'];
  myVote: PostVoteType;
  parent: CommentParent;
  /** Post under which this comment is */
  post: Post;
  publishedAt: Scalars['DateTime']['output'];
  siblingId?: Maybe<Scalars['ID']['output']>;
  state: PostState;
  text?: Maybe<Scalars['String']['output']>;
  textDelta?: Maybe<Scalars['String']['output']>;
  textHtml?: Maybe<Scalars['String']['output']>;
  textMarkdown?: Maybe<Scalars['String']['output']>;
  /** User that wrote this comment */
  user: User;
  voteScore: Scalars['Int']['output'];
};

export type CommentAttributesInput = {
  assets: Array<PostAssetInput>;
  text: Scalars['String']['input'];
  textDelta?: InputMaybe<Scalars['String']['input']>;
  textHtml: Scalars['String']['input'];
};

export type CommentConnection = {
  __typename?: 'CommentConnection';
  /** A list of nodes with data. */
  nodes: Array<Comment>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

export type CommentCreatePayload = {
  __typename?: 'CommentCreatePayload';
  comment?: Maybe<Comment>;
};

/** Notification for comments and replies. */
export type CommentNotification = Notification & {
  __typename?: 'CommentNotification';
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  commentId: Scalars['ID']['output'];
  /** User that commented */
  commenter: User;
  community?: Maybe<Community>;
  /** @deprecated Do not use this anymore */
  communityId?: Maybe<Scalars['ID']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  /**
   * The post which this comment belongs
   * @deprecated Use postId instead
   */
  post: Post;
  postId: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * Id of the comment. Use query `comment` to fetch this comment entity
   * @deprecated Will be deleted, used `commentId` instead
   */
  targetId?: Maybe<Scalars['ID']['output']>;
  type: CommentNotificationType;
};

export enum CommentNotificationType {
  NEW_COMMENT = 'NEW_COMMENT',
  NEW_REPLY = 'NEW_REPLY',
  NEW_REPLY_TO_REPLY = 'NEW_REPLY_TO_REPLY'
}

export type CommentParent = Comment | CompleteContentPost | LimitedContentPost;

export type CommentUpdatePayload = {
  __typename?: 'CommentUpdatePayload';
  comment?: Maybe<Comment>;
};

export type Community = {
  __typename?: 'Community';
  description: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  image?: Maybe<ImageAsset>;
  isMember: Scalars['Boolean']['output'];
  isVerified: Scalars['Boolean']['output'];
  membersCount: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  owner: User;
  slug: Scalars['String']['output'];
  /** Unstable, might be moved to a different type, something like CommunityDetails */
  slugEditableAfter: Scalars['DateTime']['output'];
  threadsCount: Scalars['Int']['output'];
  type: CommunityType;
};

/** Notification for comments and replies. */
export type CommunityCommentNotification = Notification & {
  __typename?: 'CommunityCommentNotification';
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  commentId: Scalars['ID']['output'];
  /** User that commented */
  commenter: User;
  community: Community;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * Id of the comment. Use query `comment` to fetch this comment entity
   * @deprecated Will be deleted, used `commentId` instead
   */
  targetId?: Maybe<Scalars['ID']['output']>;
  threadId: Scalars['ID']['output'];
  type: CommunityCommentNotificationType;
};

export enum CommunityCommentNotificationType {
  NEW_COMMENT = 'NEW_COMMENT',
  NEW_REPLY = 'NEW_REPLY',
  NEW_REPLY_TO_REPLY = 'NEW_REPLY_TO_REPLY'
}

export type CommunityConnection = {
  __typename?: 'CommunityConnection';
  /** A list of nodes with data. */
  nodes: Array<Community>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

export type CommunityCreatePayload = {
  __typename?: 'CommunityCreatePayload';
  community?: Maybe<Community>;
};

export type CommunityFilter = {
  /** Setting this to false currently does the same thing as settings this to true */
  isMember?: InputMaybe<Scalars['Boolean']['input']>;
  ownerId?: InputMaybe<Scalars['String']['input']>;
};

export type CommunitySort = {
  by?: InputMaybe<CommunitySortFields>;
  order?: InputMaybe<SortDirection>;
};

export enum CommunitySortFields {
  CREATED_AT = 'CREATED_AT',
  THREADS_COUNT = 'THREADS_COUNT'
}

/** Notifications for new threads */
export type CommunityThreadNotification = Notification & {
  __typename?: 'CommunityThreadNotification';
  actorCount: Scalars['Int']['output'];
  /** Author of the thread */
  author?: Maybe<User>;
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  community: Community;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * This should be the id of the entity or event that triggered this notification
   * @deprecated Will be deleted, no reason for this to be here
   */
  targetId?: Maybe<Scalars['ID']['output']>;
  threadId: Scalars['ID']['output'];
  type: CommunityThreadNotificationType;
};

export enum CommunityThreadNotificationType {
  NEW_THREAD = 'NEW_THREAD'
}

export enum CommunityType {
  CONNECTED = 'CONNECTED',
  FREE = 'FREE'
}

export type CommunityUpdateInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<ImageInput>;
  name?: InputMaybe<Scalars['String']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<CommunityType>;
};

export type CommunityUpdatePayload = {
  __typename?: 'CommunityUpdatePayload';
  community?: Maybe<Community>;
};

/** Represents a post created by a creator that user can completely view. */
export type CompleteContentPost = Post & {
  __typename?: 'CompleteContentPost';
  assets: Array<PostAsset>;
  categories: Array<Category>;
  comments: CommentConnection;
  /** If the post is a thread, then this will be filled with the community it belongs to */
  community?: Maybe<Community>;
  counts: PostCounts;
  hasPreview: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  isAgeRestricted: Scalars['Boolean']['output'];
  /** Is not null only for author of the post */
  isExcludedFromRss?: Maybe<Scalars['Boolean']['output']>;
  isSponsored: Scalars['Boolean']['output'];
  markdown?: Maybe<Scalars['String']['output']>;
  myVote: PostVoteType;
  pinnedAt?: Maybe<Scalars['DateTime']['output']>;
  poll?: Maybe<Poll>;
  /** Returns posts that are either before or after this post. Sorted by PUBLISHED_AT. */
  posts: PostConnection;
  publishedAt: Scalars['DateTime']['output'];
  savedPostInfo?: Maybe<SavedPostInfo>;
  state: PostState;
  text?: Maybe<Scalars['String']['output']>;
  /** @deprecated Will be deleted, no reason for this to be here */
  textDelta?: Maybe<Scalars['String']['output']>;
  textHtml?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  /** Author of the post */
  user: User;
  voteScore: Scalars['Int']['output'];
};


/** Represents a post created by a creator that user can completely view. */
export type CompleteContentPostCommentsArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  sortDirection?: InputMaybe<SortDirection>;
};


/** Represents a post created by a creator that user can completely view. */
export type CompleteContentPostPostsArgs = {
  direction?: InputMaybe<SortDirection>;
  first?: InputMaybe<Scalars['Int']['input']>;
  reverse?: InputMaybe<Scalars['Boolean']['input']>;
};

/** Represents information about creator settings, such as currency, etc. */
export type CreatorDetails = {
  __typename?: 'CreatorDetails';
  stripeAccountActive: Scalars['Boolean']['output'];
  stripeAccountId: Scalars['String']['output'];
  stripeAccountOnboarded: Scalars['Boolean']['output'];
  stripeRequirements?: Maybe<StripeRequirements>;
};

export enum Currency {
  AUD = 'AUD',
  CHF = 'CHF',
  CZK = 'CZK',
  EUR = 'EUR',
  GBP = 'GBP',
  NZD = 'NZD',
  PLN = 'PLN',
  RON = 'RON',
  USD = 'USD'
}

export type ExpectedIncome = {
  __typename?: 'ExpectedIncome';
  grossIncomeCents: Scalars['Int']['output'];
  netIncomeCents: Scalars['Int']['output'];
};

export enum FeaturedCategories {
  NEW_CREATORS = 'NEW_CREATORS',
  POPULAR = 'POPULAR',
  RECENTLY_ACTIVE = 'RECENTLY_ACTIVE',
  TRENDING = 'TRENDING'
}

export type GenericMutationPayload = {
  __typename?: 'GenericMutationPayload';
  /** Whether the operation was successful. */
  success: Scalars['Boolean']['output'];
};

/** Generic notification that does not have specific variant. */
export type GenericNotification = Notification & {
  __typename?: 'GenericNotification';
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  targetId?: Maybe<Scalars['ID']['output']>;
  type: Scalars['String']['output'];
};

export type GjirafaAssetQuality = {
  __typename?: 'GjirafaAssetQuality';
  duration: Scalars['Float']['output'];
  quality: Scalars['String']['output'];
  size: Scalars['BigInt']['output'];
};

export enum GjirafaLivestreamStatus {
  INTERRUPTED = 'INTERRUPTED',
  LIVE = 'LIVE',
  OFFLINE = 'OFFLINE',
  PROCESSING = 'PROCESSING'
}

export enum GjirafaQualityTypeStatus {
  COMPLETE = 'COMPLETE',
  /** After 5 retries file can’t be encoded. */
  ERROR = 'ERROR',
  /** Output on the template, is not applicable for that specific input. */
  NOT_APPLICABLE = 'NOT_APPLICABLE',
  /** When some input fails to be encoded. */
  PARTIALLY_COMPLETED = 'PARTIALLY_COMPLETED',
  PROCESSING = 'PROCESSING'
}

export type ImageAsset = {
  __typename?: 'ImageAsset';
  fileName?: Maybe<Scalars['String']['output']>;
  fileSize?: Maybe<Scalars['Int']['output']>;
  height: Scalars['Int']['output'];
  hidden: Scalars['Boolean']['output'];
  url: Scalars['String']['output'];
  width: Scalars['Int']['output'];
};

export type ImageInput = {
  fileName?: InputMaybe<Scalars['String']['input']>;
  fileSize?: InputMaybe<Scalars['Int']['input']>;
  height: Scalars['Int']['input'];
  url: Scalars['String']['input'];
  width: Scalars['Int']['input'];
};

/** Represents a creator's post that user cannot view since he does not subscribe the creator */
export type LimitedContentPost = Post & {
  __typename?: 'LimitedContentPost';
  categories: Array<Category>;
  counts: PostCounts;
  id: Scalars['ID']['output'];
  pinnedAt?: Maybe<Scalars['DateTime']['output']>;
  /** Returns posts that are either before or after this post. Sorted by PUBLISHED_AT. */
  posts: PostConnection;
  publishedAt: Scalars['DateTime']['output'];
  savedPostInfo?: Maybe<SavedPostInfo>;
  state: PostState;
  /** Author of the post */
  user: User;
};


/** Represents a creator's post that user cannot view since he does not subscribe the creator */
export type LimitedContentPostPostsArgs = {
  direction?: InputMaybe<SortDirection>;
  first?: InputMaybe<Scalars['Int']['input']>;
  reverse?: InputMaybe<Scalars['Boolean']['input']>;
};

/** Represents details about gjirafa livestreams */
export type LivestreamDetails = {
  __typename?: 'LivestreamDetails';
  /** Not sure in which cases is this field nullable */
  playbackUrl?: Maybe<Scalars['String']['output']>;
  streamKey: Scalars['String']['output'];
  streamUrl: Scalars['String']['output'];
};

/** Represents a direct message which is attached to message thread. */
export type Message = {
  __typename?: 'Message';
  assets: Array<MessageAsset>;
  id: Scalars['ID']['output'];
  price?: Maybe<Scalars['Int']['output']>;
  sentAt: Scalars['DateTime']['output'];
  sentBy: User;
  text: Scalars['String']['output'];
};

export type MessageAsset = MessageLockedAsset | PostDocumentAsset | PostGjirafaAsset | PostImageAsset;

export type MessageAssetInput = {
  document?: InputMaybe<PostDocumentAssetInput>;
  gjirafa?: InputMaybe<PostGjirafaAssetInput>;
  image?: InputMaybe<PostImageAssetInput>;
};

export type MessageConnection = {
  __typename?: 'MessageConnection';
  /** A list of nodes with data. */
  nodes: Array<Message>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

export type MessageCreateInput = {
  assets?: InputMaybe<Array<MessageAssetInput>>;
  priceCents?: InputMaybe<Scalars['Int']['input']>;
  text: Scalars['String']['input'];
};

export type MessageCreatePayload = {
  __typename?: 'MessageCreatePayload';
  message?: Maybe<Message>;
};

export type MessageLockedAsset = {
  __typename?: 'MessageLockedAsset';
  price?: Maybe<Scalars['Int']['output']>;
};

/** Notification for messages. */
export type MessageNotification = Notification & {
  __typename?: 'MessageNotification';
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  /** Message that triggered this notification */
  message?: Maybe<Message>;
  messageId: Scalars['ID']['output'];
  messageThreadId: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * Id of the message that trigger this notification
   * @deprecated Will be deleted, use messageId instead
   */
  targetId?: Maybe<Scalars['ID']['output']>;
  type: MessageNotificationType;
  /** User that triggered this notification, for example user that bought a paid message */
  user: User;
};

export enum MessageNotificationType {
  PAID_POST = 'PAID_POST'
}

/** Represents an active group of messages among users in a single thread. */
export type MessageThread = {
  __typename?: 'MessageThread';
  /** Flag if user can send message to the message thread */
  canMessage: Scalars['Boolean']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  lastMessage?: Maybe<Message>;
  participants: Array<User>;
  seenAt?: Maybe<Scalars['DateTime']['output']>;
};

export type MessageThreadConnection = {
  __typename?: 'MessageThreadConnection';
  /** A list of nodes with data. */
  nodes: Array<MessageThread>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

export type MessageThreadUpdateInput = {
  checked?: InputMaybe<Scalars['Boolean']['input']>;
};

export type MessageThreadUpdatePayload = {
  __typename?: 'MessageThreadUpdatePayload';
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type MessageThreadUpsertInput = {
  participantIds: Array<Scalars['ID']['input']>;
};

export type MessageThreadUpsertPayload = {
  __typename?: 'MessageThreadUpsertPayload';
  messageThread?: Maybe<MessageThread>;
};

export type Mutation = {
  __typename?: 'Mutation';
  /**
   * Update user's timestamp for given asset id.
   * If the timestamp is null, the asset is removed from in progress watch state.
   */
  assetTimestampUpdate?: Maybe<GenericMutationPayload>;
  /** Order categories. Categories will be ordered in the same order as the passed ids. */
  categoriesOrder?: Maybe<GenericMutationPayload>;
  /** Create a new category with given name */
  categoryCreate: CategoryPayload;
  /** Delete existing category */
  categoryDelete: GenericMutationPayload;
  /** Update existing category */
  categoryUpdate: CategoryPayload;
  /**
   * Creates a comment under given parent, can be either post or another comment.
   * Only two levels of comments are supported.
   */
  commentCreate: CommentCreatePayload;
  /** Delete a comment and recursively all children */
  commentDelete?: Maybe<GenericMutationPayload>;
  /** Updates given comment. User can only update comments he created */
  commentUpdate: CommentUpdatePayload;
  communityCreate: CommunityCreatePayload;
  communityJoin?: Maybe<GenericMutationPayload>;
  communityLeave?: Maybe<GenericMutationPayload>;
  communityUpdate: CommunityUpdatePayload;
  /** Create a message in given thread */
  messageCreate: MessageCreatePayload;
  /** Marks every user's message thread as seen */
  messageThreadMarkAllSeen: GenericMutationPayload;
  /**
   * Update message thread with given id.
   * Set flag checked to true if you want to update checkedAt timestamp for the authenticated user.
   */
  messageThreadUpdate: MessageThreadUpdatePayload;
  /** Create a new message thread or get existing one */
  messageThreadUpsert: MessageThreadUpsertPayload;
  /** Marks every user's notification as seen */
  notificationMarkAllSeen?: Maybe<GenericMutationPayload>;
  /** Update user's notification settings, null or undefined values will be replaced with current values */
  notificationSettingsUpdate: NotificationSettingsUpdatePayload;
  /** Updates a notification. */
  notificationUpdate?: Maybe<GenericMutationPayload>;
  pollCastVotes?: Maybe<GenericMutationPayload>;
  pollEnd?: Maybe<GenericMutationPayload>;
  /** Add given post to user's library. User must subscribe post's creator. */
  postAddToLibrary: PostAddToLibraryPayload;
  postCastVote?: Maybe<GenericMutationPayload>;
  /** Create a post */
  postCreate: PostCreatePayload;
  /** Delete a post and recursively all its comments */
  postDelete?: Maybe<GenericMutationPayload>;
  /** Remove saved post from user's library. */
  postRemoveFromLibrary?: Maybe<GenericMutationPayload>;
  /** Updates a post with given id */
  postUpdate: PostUpdatePayload;
  /** Generates a RSS feed url for the user. */
  rssFeedUrlGenerate: RssFeedUrlGeneratePayload;
  /** Currently revokes all sessions except the current one. */
  sessionRevoke?: Maybe<GenericMutationPayload>;
  /** Accept a subscribe request */
  subscribeRequestAccept?: Maybe<GenericMutationPayload>;
  /** Accept every pending request */
  subscribeRequestAcceptAll?: Maybe<GenericMutationPayload>;
  /** Cancel my own subscribe request */
  subscribeRequestCancel?: Maybe<GenericMutationPayload>;
  /** Create a subscribe request to a creator */
  subscribeRequestCreate?: Maybe<GenericMutationPayload>;
  /** Decline a subscribe request */
  subscribeRequestDecline?: Maybe<GenericMutationPayload>;
  /** Marks every user's subscribe request as seen */
  subscribeRequestMarkAllSeen?: Maybe<GenericMutationPayload>;
  /** Cancel given creator's subscriber subscription. Can only be called by creators for their subscribers. */
  subscriberDelete: SubscriptionDeletePayload;
  /** Cancel user's subscription to the given creator */
  subscriptionCancel: SubscriptionDeletePayload;
  /** Renew user's subscription to the given creator */
  subscriptionRenew: SubscriptionRenewPayload;
  /**
   * Updates user's information.
   * - Bio: Less than 1500 chars
   * - Path: Longer than 2 chars, must match [a-z0-9]+, must not be one of:
   * "assets", "create", "hero", "herohero", "login", "post", "public", "search", "services".
   * - Name: Longer than 2 chars
   * @deprecated Field no longer supported
   */
  userDetailsUpdate: UserDetailsUpdatePayload;
  /**
   * Updates user's information.
   * - Bio: Less than 1500 chars
   * - Path: Longer than 2 chars, must match [a-z0-9]+, must not be one of:
   * "assets", "create", "hero", "herohero", "login", "post", "public", "search", "services".
   * - Name: Longer than 2 chars
   */
  viewerUpdate: UserDetailsUpdatePayload;
  /** Update user's welcome message */
  welcomeMessageUpdate: WelcomeMessageUpdatePayload;
};


export type MutationAssetTimestampUpdateArgs = {
  assetId: Scalars['ID']['input'];
  postId?: InputMaybe<Scalars['ID']['input']>;
  timestamp?: InputMaybe<Scalars['Float']['input']>;
};


export type MutationCategoriesOrderArgs = {
  input: CategoriesOrderInput;
};


export type MutationCategoryCreateArgs = {
  input: CategoryCreateInput;
};


export type MutationCategoryDeleteArgs = {
  categoryId: Scalars['ID']['input'];
};


export type MutationCategoryUpdateArgs = {
  id: Scalars['String']['input'];
  input: CategoryUpdateInput;
};


export type MutationCommentCreateArgs = {
  attributes: CommentAttributesInput;
  parentId: Scalars['ID']['input'];
  siblingId?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationCommentDeleteArgs = {
  commentId: Scalars['ID']['input'];
};


export type MutationCommentUpdateArgs = {
  attributes: CommentAttributesInput;
  commentId: Scalars['ID']['input'];
};


export type MutationCommunityJoinArgs = {
  communityId: Scalars['ID']['input'];
};


export type MutationCommunityLeaveArgs = {
  communityId: Scalars['ID']['input'];
};


export type MutationCommunityUpdateArgs = {
  communityId: Scalars['ID']['input'];
  input: CommunityUpdateInput;
};


export type MutationMessageCreateArgs = {
  input: MessageCreateInput;
  messageThreadId: Scalars['ID']['input'];
};


export type MutationMessageThreadUpdateArgs = {
  input: MessageThreadUpdateInput;
  messageThreadId: Scalars['ID']['input'];
};


export type MutationMessageThreadUpsertArgs = {
  input: MessageThreadUpsertInput;
};


export type MutationNotificationSettingsUpdateArgs = {
  input: NotificationSettingsUpdateInput;
};


export type MutationNotificationUpdateArgs = {
  id: Scalars['ID']['input'];
  input: NotificationUpdateInput;
};


export type MutationPollCastVotesArgs = {
  input: PollCastVotesInput;
  pollId: Scalars['ID']['input'];
};


export type MutationPollEndArgs = {
  pollId: Scalars['ID']['input'];
};


export type MutationPostAddToLibraryArgs = {
  postId: Scalars['ID']['input'];
};


export type MutationPostCastVoteArgs = {
  input: PostCastVote;
  postId: Scalars['ID']['input'];
};


export type MutationPostCreateArgs = {
  attributes: PostCreateInput;
};


export type MutationPostDeleteArgs = {
  postId: Scalars['ID']['input'];
};


export type MutationPostRemoveFromLibraryArgs = {
  postId: Scalars['ID']['input'];
};


export type MutationPostUpdateArgs = {
  attributes: PostUpdateInput;
  postId: Scalars['ID']['input'];
};


export type MutationRssFeedUrlGenerateArgs = {
  creatorId: Scalars['String']['input'];
};


export type MutationSubscribeRequestAcceptArgs = {
  id: Scalars['ID']['input'];
};


export type MutationSubscribeRequestCancelArgs = {
  creatorId: Scalars['ID']['input'];
};


export type MutationSubscribeRequestCreateArgs = {
  input: SubscribeRequestCreateInput;
};


export type MutationSubscribeRequestDeclineArgs = {
  id: Scalars['ID']['input'];
};


export type MutationSubscriberDeleteArgs = {
  subscriberId: Scalars['ID']['input'];
};


export type MutationSubscriptionCancelArgs = {
  creatorId: Scalars['ID']['input'];
};


export type MutationSubscriptionRenewArgs = {
  creatorId: Scalars['ID']['input'];
};


export type MutationUserDetailsUpdateArgs = {
  userDetails: UserDetailsUpdateInput;
};


export type MutationViewerUpdateArgs = {
  userDetails: UserDetailsUpdateInput;
};


export type MutationWelcomeMessageUpdateArgs = {
  input: WelcomeMessageUpdateInput;
};

export type Notification = {
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * This should be the id of the entity or event that triggered this notification
   * @deprecated Will be deleted, no reason for this to be here
   */
  targetId?: Maybe<Scalars['ID']['output']>;
};

export type NotificationConnection = {
  __typename?: 'NotificationConnection';
  /** A list of nodes with data. */
  nodes: Array<Notification>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

export type NotificationFilter = {
  categories?: InputMaybe<Array<NotificationTypeCategory>>;
};

/** Everything related to user's notification settings. */
export type NotificationSettings = {
  __typename?: 'NotificationSettings';
  emailNewDm: Scalars['Boolean']['output'];
  emailNewPost: Scalars['Boolean']['output'];
  newsletter: Scalars['Boolean']['output'];
  pushNewComment: Scalars['Boolean']['output'];
  pushNewMessage: Scalars['Boolean']['output'];
  pushNewPost: Scalars['Boolean']['output'];
  termsChanged: Scalars['Boolean']['output'];
};

export type NotificationSettingsUpdateInput = {
  emailNewDm?: InputMaybe<Scalars['Boolean']['input']>;
  emailNewPost?: InputMaybe<Scalars['Boolean']['input']>;
  newsletter?: InputMaybe<Scalars['Boolean']['input']>;
  pushNewComment?: InputMaybe<Scalars['Boolean']['input']>;
  pushNewMessage?: InputMaybe<Scalars['Boolean']['input']>;
  pushNewPost?: InputMaybe<Scalars['Boolean']['input']>;
  termsChanged?: InputMaybe<Scalars['Boolean']['input']>;
};

export type NotificationSettingsUpdatePayload = {
  __typename?: 'NotificationSettingsUpdatePayload';
  success: Scalars['Boolean']['output'];
};

export enum NotificationTypeCategory {
  COMMENT = 'COMMENT',
  COMMUNITY = 'COMMUNITY',
  POST = 'POST',
  REQUEST = 'REQUEST',
  SUBSCRIPTION = 'SUBSCRIPTION'
}

export type NotificationUpdateInput = {
  /** The time when notification was marked as checked. */
  checkedAt?: InputMaybe<Scalars['DateTime']['input']>;
  /** The time when notification was marked as seen. */
  seenAt?: InputMaybe<Scalars['DateTime']['input']>;
};

/**
 * Returns information about pagination in a connection, in accordance with the Shopify pagination model, see
 * https://shopify.dev/docs/api/admin-graphql/2023-07/objects/PageInfo.
 */
export type PageInfo = {
  __typename?: 'PageInfo';
  /** The cursor corresponding to the last node */
  endCursor?: Maybe<Scalars['String']['output']>;
  /** Whether there are more pages to fetch following the current page */
  hasNextPage: Scalars['Boolean']['output'];
  /** The cursor corresponding to the first node */
  startCursor?: Maybe<Scalars['String']['output']>;
};

export type Poll = {
  __typename?: 'Poll';
  deadline: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  options: Array<PollOption>;
};

export type PollCastVotesInput = {
  votes: Array<Scalars['ID']['input']>;
};

export type PollOption = {
  __typename?: 'PollOption';
  hasVotedFor: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  voteCount: Scalars['Int']['output'];
};

export type Post = {
  /** Post categories, in some cases this can be incorrectly empty, for example when fetching posts from library */
  categories: Array<Category>;
  counts: PostCounts;
  id: Scalars['ID']['output'];
  pinnedAt?: Maybe<Scalars['DateTime']['output']>;
  /** Returns posts that are either before or after this post. Sorted by PUBLISHED_AT. */
  posts: PostConnection;
  publishedAt: Scalars['DateTime']['output'];
  savedPostInfo?: Maybe<SavedPostInfo>;
  state: PostState;
  /** Author of the post */
  user: User;
};


export type PostPostsArgs = {
  direction?: InputMaybe<SortDirection>;
  first?: InputMaybe<Scalars['Int']['input']>;
  reverse?: InputMaybe<Scalars['Boolean']['input']>;
};

export type PostAddToLibraryPayload = {
  __typename?: 'PostAddToLibraryPayload';
  savedPost?: Maybe<SavedPost>;
};

export type PostAsset = PostBunnyAsset | PostDocumentAsset | PostEmptyAsset | PostGjirafaAsset | PostGjirafaLivestreamAsset | PostImageAsset | PostYoutubeAsset;

/**
 * For some reason there is no input union, so we have to resort to this solution.
 * At least one must be set, otherwise validation error is thrown.
 * We must wait for https://github.com/graphql/graphql-spec/issues/488 to be implemented.
 */
export type PostAssetInput = {
  document?: InputMaybe<PostDocumentAssetInput>;
  gjirafa?: InputMaybe<PostGjirafaAssetInput>;
  gjirafaLivestream?: InputMaybe<PostGjirafaLivestreamAssetInput>;
  image?: InputMaybe<PostImageAssetInput>;
};

export type PostBunnyAsset = {
  __typename?: 'PostBunnyAsset';
  url: Scalars['String']['output'];
};

export type PostCastVote = {
  voteType: PostVoteType;
};

export type PostConnection = {
  __typename?: 'PostConnection';
  /** A list of nodes with data. */
  nodes: Array<Post>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

export type PostCounts = {
  __typename?: 'PostCounts';
  comments: Scalars['Int']['output'];
  replies: Scalars['Int']['output'];
};

export type PostCreateInput = {
  assets: Array<PostAssetInput>;
  categories: Array<Scalars['String']['input']>;
  communityId?: InputMaybe<Scalars['ID']['input']>;
  hasPreview?: InputMaybe<Scalars['Boolean']['input']>;
  isAgeRestricted?: InputMaybe<Scalars['Boolean']['input']>;
  isSponsored?: InputMaybe<Scalars['Boolean']['input']>;
  /** Cannot be in the past */
  publishedAt?: InputMaybe<Scalars['DateTime']['input']>;
  text: Scalars['String']['input'];
  textDelta?: InputMaybe<Scalars['String']['input']>;
  textHtml: Scalars['String']['input'];
  title?: InputMaybe<Scalars['String']['input']>;
};

export type PostCreatePayload = {
  __typename?: 'PostCreatePayload';
  post?: Maybe<CompleteContentPost>;
};

export type PostDocumentAsset = {
  __typename?: 'PostDocumentAsset';
  name: Scalars['String']['output'];
  thumbnail?: Maybe<PostImageAsset>;
  /** @deprecated Will be replaced by thumbnail field */
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
  type: PostDocumentType;
  url: Scalars['String']['output'];
};

export type PostDocumentAssetInput = {
  name: Scalars['String']['input'];
  thumbnail?: InputMaybe<PostImageAssetInput>;
  thumbnailUrl?: InputMaybe<Scalars['String']['input']>;
  type: PostDocumentType;
  url: Scalars['String']['input'];
};

export enum PostDocumentType {
  ARW = 'ARW',
  BLEND = 'BLEND',
  DAE = 'DAE',
  DOCX = 'DOCX',
  EPUB = 'EPUB',
  FBX = 'FBX',
  GLB = 'GLB',
  GLTF = 'GLTF',
  GP5 = 'GP5',
  GPX = 'GPX',
  MIDI = 'MIDI',
  OBJ = 'OBJ',
  PDF = 'PDF',
  PLY = 'PLY',
  PPTX = 'PPTX',
  RAW = 'RAW',
  STL = 'STL',
  XLSX = 'XLSX',
  _3DS = '_3DS'
}

export type PostEmptyAsset = {
  __typename?: 'PostEmptyAsset';
  /** This is a dummy value since types in GraphQL must have at least one field */
  dummy?: Maybe<Scalars['String']['output']>;
};

/** Post filtering options. */
export type PostFilter = {
  categoryId?: InputMaybe<Scalars['String']['input']>;
  communityId?: InputMaybe<Scalars['ID']['input']>;
  creatorId?: InputMaybe<Scalars['String']['input']>;
  excludedCreatorIds?: InputMaybe<Array<Scalars['String']['input']>>;
  /** If both creatorId communityId are null, this is set to SUBSCRIBED_CREATORS by default */
  source?: InputMaybe<PostSource>;
  /** Search string to look for. */
  term?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<PostTypeFilter>;
};

export type PostGjirafaAsset = {
  __typename?: 'PostGjirafaAsset';
  audioByteSize?: Maybe<Scalars['Int']['output']>;
  /** eg.: https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/mp3/320kbps.mp3 */
  audioStaticUrl?: Maybe<Scalars['String']['output']>;
  /** eg.: https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/hls/360p/index.m3u8 */
  audioStreamUrl?: Maybe<Scalars['String']['output']>;
  duration: Scalars['Float']['output'];
  gjirafaId: Scalars['String']['output'];
  hasAudio: Scalars['Boolean']['output'];
  hasVideo: Scalars['Boolean']['output'];
  height: Scalars['Int']['output'];
  hidden: Scalars['Boolean']['output'];
  /** @deprecated Use gjirafaId instead */
  id: Scalars['String']['output'];
  isLivestreamRecording: Scalars['Boolean']['output'];
  key?: Maybe<Scalars['String']['output']>;
  keyId: Scalars['String']['output'];
  previewAnimatedUrl?: Maybe<Scalars['String']['output']>;
  previewStaticUrl?: Maybe<Scalars['String']['output']>;
  previewStripUrl?: Maybe<Scalars['String']['output']>;
  progressTillCompleteness: Scalars['Int']['output'];
  progressTillReadiness: Scalars['Int']['output'];
  status: GjirafaQualityTypeStatus;
  thumbnail?: Maybe<PostImageAsset>;
  /** @deprecated Will be replaced by thumbnail field */
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
  /** User's timestamp from his media store, returns null if unauthenticated or user has no timestamp for the asset */
  timestamp?: Maybe<Scalars['Float']['output']>;
  /** eg.:https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/hls/master_file.m3u8 */
  videoStreamUrl?: Maybe<Scalars['String']['output']>;
  width: Scalars['Int']['output'];
};

export type PostGjirafaAssetInput = {
  id: Scalars['String']['input'];
  thumbnail?: InputMaybe<PostImageAssetInput>;
  thumbnailUrl?: InputMaybe<Scalars['String']['input']>;
};

export type PostGjirafaLivestreamAsset = {
  __typename?: 'PostGjirafaLivestreamAsset';
  channelPublicId: Scalars['String']['output'];
  gjirafaId: Scalars['String']['output'];
  /** @deprecated Use gjirafaId instead */
  id: Scalars['String']['output'];
  playbackUrl: Scalars['String']['output'];
  startDateUTC?: Maybe<Scalars['DateTime']['output']>;
  status: GjirafaLivestreamStatus;
  thumbnail?: Maybe<PostImageAsset>;
  /** @deprecated Will be replaced by thumbnail field */
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
};

export type PostGjirafaLivestreamAssetInput = {
  id: Scalars['String']['input'];
  thumbnail?: InputMaybe<PostImageAssetInput>;
  thumbnailUrl?: InputMaybe<Scalars['String']['input']>;
};

export type PostImageAsset = {
  __typename?: 'PostImageAsset';
  height: Scalars['Int']['output'];
  url: Scalars['String']['output'];
  width: Scalars['Int']['output'];
};

export type PostImageAssetInput = {
  height: Scalars['Int']['input'];
  url: Scalars['String']['input'];
  width: Scalars['Int']['input'];
};

/** Notifications for new posts */
export type PostNotification = Notification & {
  __typename?: 'PostNotification';
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  community?: Maybe<Community>;
  /** @deprecated Do not use this anymore */
  communityId?: Maybe<Scalars['ID']['output']>;
  createdAt: Scalars['DateTime']['output'];
  /** Creator of the post */
  creator?: Maybe<User>;
  id: Scalars['ID']['output'];
  /** Post that triggered this notification */
  post?: Maybe<Post>;
  postId: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * Id of the post that triggered this notification
   * @deprecated Will be deleted, use `postId` instead
   */
  targetId?: Maybe<Scalars['ID']['output']>;
  type: PostNotificationType;
};

export enum PostNotificationType {
  NEW_LIVESTREAM = 'NEW_LIVESTREAM',
  NEW_POST = 'NEW_POST',
  NEW_THREAD = 'NEW_THREAD'
}

export type PostPreviewAsset = PostPreviewDocumentAsset | PostPreviewGjirafaAsset | PostPreviewGjirafaLiveAsset | PostPreviewImageAsset;

export type PostPreviewDocumentAsset = {
  __typename?: 'PostPreviewDocumentAsset';
  name: Scalars['String']['output'];
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
  type: PostDocumentType;
};

export type PostPreviewGjirafaAsset = {
  __typename?: 'PostPreviewGjirafaAsset';
  duration: Scalars['Float']['output'];
  height: Scalars['Int']['output'];
  previewAnimatedUrl?: Maybe<Scalars['String']['output']>;
  previewStaticUrl?: Maybe<Scalars['String']['output']>;
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
  type: PostPreviewGjirafaType;
  width: Scalars['Int']['output'];
};

export type PostPreviewGjirafaLiveAsset = {
  __typename?: 'PostPreviewGjirafaLiveAsset';
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
};

export enum PostPreviewGjirafaType {
  AUDIO = 'AUDIO',
  VIDEO = 'VIDEO'
}

export type PostPreviewImageAsset = {
  __typename?: 'PostPreviewImageAsset';
  height: Scalars['Int']['output'];
  url: Scalars['String']['output'];
  width: Scalars['Int']['output'];
};

export type PostSort = {
  by?: InputMaybe<PostSortFields>;
  order?: InputMaybe<SortDirection>;
};

export enum PostSortFields {
  /** This is the default we are currently using on the profile page (PINNED_AT, desc). */
  PINNED_AT = 'PINNED_AT',
  /** Sorting only by PUBLISHED_AT ignores PINNED_AT. */
  PUBLISHED_AT = 'PUBLISHED_AT',
  /** Sort by term relevance. Term must be set in the filter. */
  TERM_RELEVANCE = 'TERM_RELEVANCE',
  /** Sorts by number of post views. Secondary sort is published_at. */
  VIEWS = 'VIEWS',
  /** Sorts by vote score. Secondary sort is published_at. */
  VOTE_SCORE = 'VOTE_SCORE',
  /**
   * Sorts by when user watched the post. Behaves the same way as query `postsInProgress` but includes posts that
   * user has not watched which are sorted by PUBLISHED_AT.
   */
  WATCHED_AT = 'WATCHED_AT'
}

export enum PostSource {
  JOINED_COMMUNITIES = 'JOINED_COMMUNITIES',
  SUBSCRIBED_CREATORS = 'SUBSCRIBED_CREATORS'
}

export enum PostState {
  DELETED = 'DELETED',
  PROCESSING = 'PROCESSING',
  PUBLISHED = 'PUBLISHED',
  REVISION = 'REVISION',
  SCHEDULED = 'SCHEDULED'
}

export enum PostTypeFilter {
  IN_PROGRESS = 'IN_PROGRESS'
}

export type PostUpdateInput = {
  assets?: InputMaybe<Array<PostAssetInput>>;
  categories?: InputMaybe<Array<Scalars['String']['input']>>;
  hasPreview?: InputMaybe<Scalars['Boolean']['input']>;
  isAgeRestricted?: InputMaybe<Scalars['Boolean']['input']>;
  isExcludedFromRss?: InputMaybe<Scalars['Boolean']['input']>;
  isSponsored?: InputMaybe<Scalars['Boolean']['input']>;
  pinnedAt?: InputMaybe<Scalars['DateTime']['input']>;
  /** Cannot be in the past */
  publishedAt?: InputMaybe<Scalars['DateTime']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
  textDelta?: InputMaybe<Scalars['String']['input']>;
  textHtml?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type PostUpdatePayload = {
  __typename?: 'PostUpdatePayload';
  post?: Maybe<CompleteContentPost>;
};

export enum PostVoteType {
  DOWN = 'DOWN',
  NONE = 'NONE',
  UP = 'UP'
}

export type PostYoutubeAsset = {
  __typename?: 'PostYoutubeAsset';
  height?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  previewUrl?: Maybe<Scalars['String']['output']>;
  thumbnail?: Maybe<PostImageAsset>;
  /** @deprecated Will be replaced by thumbnail field */
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
  width?: Maybe<Scalars['Int']['output']>;
};

/** Represents a creator's post that user cannot view since he does not subscribe the creator */
export type PreviewContentPost = Post & {
  __typename?: 'PreviewContentPost';
  assets: Array<PostPreviewAsset>;
  categories: Array<Category>;
  counts: PostCounts;
  id: Scalars['ID']['output'];
  pinnedAt?: Maybe<Scalars['DateTime']['output']>;
  poll?: Maybe<Poll>;
  /** Returns posts that are either before or after this post. Sorted by PUBLISHED_AT. */
  posts: PostConnection;
  publishedAt: Scalars['DateTime']['output'];
  savedPostInfo?: Maybe<SavedPostInfo>;
  state: PostState;
  text?: Maybe<Scalars['String']['output']>;
  textHtml?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  /** Author of the post */
  user: User;
};


/** Represents a creator's post that user cannot view since he does not subscribe the creator */
export type PreviewContentPostPostsArgs = {
  direction?: InputMaybe<SortDirection>;
  first?: InputMaybe<Scalars['Int']['input']>;
  reverse?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Price = {
  __typename?: 'Price';
  currency: Currency;
  priceCents: Scalars['Int']['output'];
};

export type Query = {
  __typename?: 'Query';
  /** Return details of the user, must be authenticated as moderator */
  adminUserDetails: UserDetails;
  /**
   * Fetch single comment. This query should be only used from notification tab when clicking through to the comment
   * that triggered the notification.
   */
  comment: Comment;
  /**
   * Comments for a post or another comment
   *
   * Sorted in descending order of recency.
   */
  comments: CommentConnection;
  communities: CommunityConnection;
  /**
   * Fetch a single community by id or slug
   *
   * Exactly one of communityId and slug must be provided.
   */
  community: Community;
  /** Fetch viewer's expected income. */
  expectedIncome: ExpectedIncome;
  /**
   * Fetch popular creators. Paging is not supported at the moment.
   * Locale to fetch popular creators from.
   */
  featuredCreators: UserConnection;
  /** Returns 20 creators from given category. The result is randomized. */
  featuredCreatorsRandomized: RandomizedFeaturedCreatorsPayload;
  /** Fetch new posts from popular creators who have enabled post previews. */
  featuredPosts: PostConnection;
  /** Fetch trending threads, sorted by vote score, which are not older than 31 days. */
  featuredThreads: PostConnection;
  /** Use this to get sizes of all quality types for given video asset. For example if you need a size for 420p stream. */
  gjirafaVideoQualities: Array<GjirafaAssetQuality>;
  /** Returns creator's livestreams that are currently live */
  livestreams: PostConnection;
  /** Fetch a single message thread by id */
  messageThread: MessageThread;
  /**
   * List of active message threads of the current user.
   *
   * Sorted in descending order of recency.
   */
  messageThreads: MessageThreadConnection;
  /**
   * Retrieves messages from given message thread.
   *
   * Sorted in descending order of recency.
   */
  messages: MessageConnection;
  /** Returns notification settings for currently logged in user */
  notificationSettings: NotificationSettings;
  /** List of notifications objects of the current user. */
  notifications: NotificationConnection;
  /** One specific creator's post */
  post: Post;
  /**
   * Creator's posts
   *
   * Sorted in descending order of recency.
   */
  posts: PostConnection;
  /**
   * Fetch saved posts in user's library. Default behaviour is that posts from both subscribed and unsubscribed creators
   * are returned, if you want to modify the behaviour, use filter.
   * Default and only sort is by `savedAt DESC`.
   */
  savedPosts: SavedPostConnection;
  /** Search communities by given query. Paging is not supported at the moment. */
  searchCommunities: CommunityConnection;
  /** This query does not paginate yet */
  searchPosts: PostConnection;
  /** Search users by given query. Paging is not supported at the moment. */
  searchUsers: UserConnection;
  /** List of subscribe requests for the current user. */
  subscribeRequests: SubscribeRequestConnection;
  /** Subscribers of given creator */
  subscribers: SubscriptionConnection;
  /** Subscriptions of given user */
  subscriptions: SubscriptionConnection;
  /** One specific user */
  user: User;
  /** The currently authenticated user */
  viewer: UserDetails;
  /** Currently authenticated user's subscription for given creator */
  viewerSubscription?: Maybe<UserSubscriptionDetails>;
  /** Fetch user's welcome message */
  welcomeMessage: WelcomeMessage;
};


export type QueryAdminUserDetailsArgs = {
  id: Scalars['ID']['input'];
};


export type QueryCommentArgs = {
  id: Scalars['ID']['input'];
};


export type QueryCommentsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  sortDirection?: InputMaybe<SortDirection>;
};


export type QueryCommunitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<CommunityFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<CommunitySort>;
};


export type QueryCommunityArgs = {
  communityId?: InputMaybe<Scalars['ID']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryFeaturedCreatorsArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  locale?: InputMaybe<Scalars['String']['input']>;
};


export type QueryFeaturedCreatorsRandomizedArgs = {
  featuredCategory: FeaturedCategories;
};


export type QueryFeaturedPostsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryFeaturedThreadsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGjirafaVideoQualitiesArgs = {
  assetId: Scalars['ID']['input'];
};


export type QueryMessageThreadArgs = {
  messageThreadId: Scalars['ID']['input'];
};


export type QueryMessageThreadsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryMessagesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  messageThreadId: Scalars['ID']['input'];
  reverse?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryNotificationsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<NotificationFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryPostArgs = {
  id: Scalars['ID']['input'];
};


export type QueryPostsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<PostFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  reverse?: InputMaybe<Scalars['Boolean']['input']>;
  sort?: InputMaybe<PostSort>;
};


export type QuerySavedPostsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<SavedPostsFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
};


export type QuerySearchCommunitiesArgs = {
  query: Scalars['String']['input'];
};


export type QuerySearchPostsArgs = {
  filter?: InputMaybe<SearchPostsFilter>;
};


export type QuerySearchUsersArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  query: Scalars['String']['input'];
};


export type QuerySubscribeRequestsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
};


export type QuerySubscribersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  creatorId: Scalars['ID']['input'];
  first?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: SubscriptionOrderBy;
};


export type QuerySubscriptionsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<SubscriptionFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: SubscriptionOrderBy;
  userId: Scalars['ID']['input'];
};


export type QueryUserArgs = {
  id: Scalars['ID']['input'];
};


export type QueryViewerSubscriptionArgs = {
  creatorId: Scalars['ID']['input'];
};

export type RandomizedFeaturedCreatorsPayload = {
  __typename?: 'RandomizedFeaturedCreatorsPayload';
  users: Array<User>;
};

export type RequestNotification = Notification & {
  __typename?: 'RequestNotification';
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  /** Creator that user subscribed or attempted to subscribe to. */
  creator?: Maybe<User>;
  id: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * Currently always null, we might want to change this to id of the creator
   * @deprecated Will be deleted since it's always null
   */
  targetId?: Maybe<Scalars['ID']['output']>;
  type: RequestNotificationType;
};

export enum RequestNotificationType {
  SUBSCRIBE_REQUEST_ACCEPTED = 'SUBSCRIBE_REQUEST_ACCEPTED'
}

export type RssFeedUrl = {
  __typename?: 'RssFeedUrl';
  url: Scalars['String']['output'];
};

export type RssFeedUrlGeneratePayload = {
  __typename?: 'RssFeedUrlGeneratePayload';
  rssFeedUrl?: Maybe<RssFeedUrl>;
};

export type SavedPost = {
  __typename?: 'SavedPost';
  post: Post;
  savedAt: Scalars['DateTime']['output'];
};

export type SavedPostConnection = {
  __typename?: 'SavedPostConnection';
  /** A list of nodes with data. */
  nodes: Array<SavedPost>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

/**
 * Just a helper type so Post type does not have to have SavedPost type field
 * which results in weird object instantiations and recursive querying
 */
export type SavedPostInfo = {
  __typename?: 'SavedPostInfo';
  savedAt: Scalars['DateTime']['output'];
};

export type SavedPostsFilter = {
  subscribedCreatorsOnly?: InputMaybe<Scalars['Boolean']['input']>;
};

export type SearchPostsFilter = {
  query?: InputMaybe<Scalars['String']['input']>;
};

export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

export type StripeRequirements = {
  __typename?: 'StripeRequirements';
  currentlyDue: Array<Scalars['String']['output']>;
  deleted: Scalars['Boolean']['output'];
  disabledReason?: Maybe<Scalars['String']['output']>;
  errors: Array<Scalars['String']['output']>;
  eventuallyDue: Array<Scalars['String']['output']>;
  pastDue: Array<Scalars['String']['output']>;
  pendingVerification: Array<Scalars['String']['output']>;
  stripeAccountId?: Maybe<Scalars['String']['output']>;
  valid: Scalars['Boolean']['output'];
};

/** A subscribe request represents a request from a user to subscribe to a creator. */
export type SubscribeRequest = {
  __typename?: 'SubscribeRequest';
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /** User that sent the subscribe request */
  user: User;
};

/** A connection to a list of subscribe requests. */
export type SubscribeRequestConnection = {
  __typename?: 'SubscribeRequestConnection';
  /** A list of subscribe requests. */
  nodes: Array<SubscribeRequest>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

export type SubscribeRequestCreateInput = {
  /** ID of the creator to subscribe to */
  creatorId: Scalars['ID']['input'];
};

export enum SubscribeRequestState {
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  PENDING = 'PENDING'
}

/** Notifications for new subscribers. */
export type SubscriberNotification = Notification & {
  __typename?: 'SubscriberNotification';
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  /** Last subscriber that associated with this notification */
  lastSubscriber?: Maybe<User>;
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * Currently always null, we might want to change this to id of the last subscriber
   * @deprecated Will be deleted since it's always null
   */
  targetId?: Maybe<Scalars['ID']['output']>;
  type: SubscriberNotificationType;
};

export enum SubscriberNotificationType {
  NEW_SUBSCRIPTION = 'NEW_SUBSCRIPTION'
}

export type SubscriptionConnection = {
  __typename?: 'SubscriptionConnection';
  /** A list of nodes with data. */
  nodes: Array<UserSubscription>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

export enum SubscriptionCouponMethod {
  /** Free invites, trials, etc. */
  TRIAL = 'TRIAL',
  /** Gifts */
  VOUCHER = 'VOUCHER'
}

export type SubscriptionDeletePayload = {
  __typename?: 'SubscriptionDeletePayload';
  /** Deleted subscription */
  subscription: UserSubscriptionDetails;
  success: Scalars['Boolean']['output'];
};

export type SubscriptionFilter = {
  /** Default is false */
  expired?: InputMaybe<Scalars['Boolean']['input']>;
};

/**
 * Notifications for subscription related events, such as failed payments
 * when subscribing.
 */
export type SubscriptionNotification = Notification & {
  __typename?: 'SubscriptionNotification';
  actorCount: Scalars['Int']['output'];
  checkedAt?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  /** Creator that user subscribed or attempted to subscribe to. */
  creator?: Maybe<User>;
  id: Scalars['ID']['output'];
  seenAt?: Maybe<Scalars['DateTime']['output']>;
  /**
   * Currently always null, we might want to change this to id of the creator
   * @deprecated Will be deleted since it's always null
   */
  targetId?: Maybe<Scalars['ID']['output']>;
  type: SubscriptionNotificationType;
};

export enum SubscriptionNotificationType {
  CANCELLED_SUBSCRIPTION_BY_CREATOR = 'CANCELLED_SUBSCRIPTION_BY_CREATOR',
  CANCELLED_SUBSCRIPTION_ENDED = 'CANCELLED_SUBSCRIPTION_ENDED',
  CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS = 'CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS',
  CANCELLED_SUBSCRIPTION_OTHER = 'CANCELLED_SUBSCRIPTION_OTHER',
  CANCELLED_SUBSCRIPTION_REFUNDED = 'CANCELLED_SUBSCRIPTION_REFUNDED',
  CANCELLED_SUBSCRIPTION_REFUSED = 'CANCELLED_SUBSCRIPTION_REFUSED',
  PAYMENT_CARD_DECLINED = 'PAYMENT_CARD_DECLINED',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  PAYMENT_INSUFFICIENT_FUNDS = 'PAYMENT_INSUFFICIENT_FUNDS'
}

export enum SubscriptionOrderBy {
  GIFTED_FIRST = 'GIFTED_FIRST',
  HIGHEST_PRICE = 'HIGHEST_PRICE',
  LOWEST_PRICE = 'LOWEST_PRICE',
  NEWEST = 'NEWEST',
  OLDEST = 'OLDEST'
}

/** Describes relation between a user and a target user */
export enum SubscriptionRelationType {
  /** There are multiple users in the relation */
  GROUP = 'GROUP',
  /** Relation of user to himself */
  HIMSELF = 'HIMSELF',
  /** Is subscribed by the target */
  IS_SUBSCRIBED_BY = 'IS_SUBSCRIBED_BY',
  /** User is subscribed to the target */
  IS_SUBSCRIBED_TO = 'IS_SUBSCRIBED_TO',
  /** User is subscribed to the same creator as the target */
  IS_SUBSCRIBED_TO_SAME_CREATOR = 'IS_SUBSCRIBED_TO_SAME_CREATOR'
}

export type SubscriptionRenewPayload = {
  __typename?: 'SubscriptionRenewPayload';
  /** Renewed subscription */
  subscription: UserSubscriptionDetails;
  success: Scalars['Boolean']['output'];
};

export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PAST_DUE = 'PAST_DUE'
}

export enum SubscriptionType {
  /** @deprecated Irrelevant */
  INVITE = 'INVITE',
  /** @deprecated Irrelevant */
  STRIPE = 'STRIPE',
  SUBSCRIBE_REQUEST = 'SUBSCRIBE_REQUEST'
}

/** Represents information about tier, such as price, fees and currency. */
export type Tier = {
  __typename?: 'Tier';
  currency: Currency;
  default: Scalars['Boolean']['output'];
  hidden: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  priceCents: Scalars['Int']['output'];
};

/** Represents public information about a user or a creator. */
export type User = {
  __typename?: 'User';
  analytics: UserAnalytics;
  /** @deprecated This will no longer be a string, but a type that can resolve to markdown or html */
  bio: Scalars['String']['output'];
  bioMarkdown: Scalars['String']['output'];
  categories: Array<Category>;
  counts: UserCounts;
  emailPublic?: Maybe<Scalars['String']['output']>;
  /** Small creators do not have gifts allowed to prevent fraudulent behaviour */
  hasGiftsAllowed: Scalars['Boolean']['output'];
  hasRssFeed: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  image?: Maybe<ImageAsset>;
  isDeleted: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  path: Scalars['String']['output'];
  privacyPolicyEnabled: Scalars['Boolean']['output'];
  profileType: UserProfileType;
  spotifyShowId?: Maybe<Scalars['String']['output']>;
  subscribable: Scalars['Boolean']['output'];
  subscribeRequestState?: Maybe<SubscribeRequestState>;
  /** Subscription of this user for the currently logged user, null if he is not a subscriber or user is not logged in */
  subscriber?: Maybe<UserSubscriptionDetails>;
  /** Subscription of currently logged user for this user, null if no active subscription or user is not logged in */
  subscription?: Maybe<UserSubscriptionDetails>;
  tier: Tier;
  verified: Scalars['Boolean']['output'];
};

/** Everything related to analytics for the given user. */
export type UserAnalytics = {
  __typename?: 'UserAnalytics';
  facebookPixelId?: Maybe<Scalars['String']['output']>;
  ga4Stream?: Maybe<Scalars['String']['output']>;
  googleAdsConversionId?: Maybe<Scalars['String']['output']>;
  googleAdsConversionLabel?: Maybe<Scalars['String']['output']>;
  leadHub?: Maybe<Scalars['String']['output']>;
  tiktokPixelId?: Maybe<Scalars['String']['output']>;
};

/** Represents information about user's company. */
export type UserCompany = {
  __typename?: 'UserCompany';
  additionalInfo?: Maybe<Scalars['String']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  iban?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  registeredWith?: Maybe<Scalars['String']['output']>;
  swift?: Maybe<Scalars['String']['output']>;
  vatId?: Maybe<Scalars['String']['output']>;
  vatRate?: Maybe<Scalars['Int']['output']>;
  vatType?: Maybe<UserCompanyVatType>;
};

export enum UserCompanyVatType {
  EXEMPTED_FROM_VAT = 'EXEMPTED_FROM_VAT',
  NON_VAT_PAYER = 'NON_VAT_PAYER',
  VAT_PAYER = 'VAT_PAYER'
}

export type UserConnection = {
  __typename?: 'UserConnection';
  /** A list of nodes with data. */
  nodes: Array<User>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

/** Represents public information about user's counts, such as support count, supporting count, and others. */
export type UserCounts = {
  __typename?: 'UserCounts';
  ownedCommunities: Scalars['Int']['output'];
  posts: Scalars['Int']['output'];
  supporters: Scalars['Int']['output'];
  supportersThreshold?: Maybe<Scalars['Int']['output']>;
  supporting: Scalars['Int']['output'];
};

/** Represents private information about a user or a creator. */
export type UserDetails = {
  __typename?: 'UserDetails';
  /** @deprecated This will no longer be a string, but a type that can resolve to markdown or html */
  bio: Scalars['String']['output'];
  bioMarkdown: Scalars['String']['output'];
  categories: Array<Category>;
  counts: UserDetailsCounts;
  creator?: Maybe<CreatorDetails>;
  discord?: Maybe<UserDiscordSettings>;
  email?: Maybe<Scalars['String']['output']>;
  emailInvoice?: Maybe<Scalars['String']['output']>;
  emailPublic?: Maybe<Scalars['String']['output']>;
  hasPostPreviews: Scalars['Boolean']['output'];
  hasRssFeed: Scalars['Boolean']['output'];
  hasWelcomeMessageEnabled: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  image?: Maybe<ImageAsset>;
  /** If user is of age to view age restricted content */
  isOfAge: Scalars['Boolean']['output'];
  language: Scalars['String']['output'];
  livestream?: Maybe<LivestreamDetails>;
  name: Scalars['String']['output'];
  path: Scalars['String']['output'];
  pathEditableAfter: Scalars['DateTime']['output'];
  profileType: UserProfileType;
  role: UserRole;
  spotify?: Maybe<UserSpotifyDetails>;
  subscribable: Scalars['Boolean']['output'];
  tier: Tier;
  verified: Scalars['Boolean']['output'];
};

/** Represents public information about user's counts, such as support count, supporting count, and others. */
export type UserDetailsCounts = {
  __typename?: 'UserDetailsCounts';
  incomes: Scalars['Int']['output'];
  incomesClean: Scalars['Int']['output'];
  invoices: Scalars['Int']['output'];
  payments: Scalars['Int']['output'];
  pendingRequests: Scalars['Int']['output'];
  posts: Scalars['Int']['output'];
  supporters: Scalars['Int']['output'];
  supporting: Scalars['Int']['output'];
};

export type UserDetailsUpdateError = {
  __typename?: 'UserDetailsUpdateError';
  errorType: UserDetailsUpdateErrorType;
  property: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export enum UserDetailsUpdateErrorType {
  ILLEGAL_STRING = 'ILLEGAL_STRING',
  LOWERCASE_ALPHANUMERIC = 'LOWERCASE_ALPHANUMERIC',
  MAX_LENGTH_EXCEEDED = 'MAX_LENGTH_EXCEEDED',
  MIN_LENGTH_TWO = 'MIN_LENGTH_TWO',
  PATH_CHANGE_TOO_OFTEN = 'PATH_CHANGE_TOO_OFTEN',
  PATH_TAKEN = 'PATH_TAKEN'
}

export type UserDetailsUpdateInput = {
  bio?: InputMaybe<Scalars['String']['input']>;
  emailInvoice?: InputMaybe<Scalars['String']['input']>;
  emailPublic?: InputMaybe<Scalars['String']['input']>;
  hasPostPreviews?: InputMaybe<Scalars['Boolean']['input']>;
  hasWelcomeMessageEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  isOfAge?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  path?: InputMaybe<Scalars['String']['input']>;
  profileImage?: InputMaybe<UserImageInput>;
};

export type UserDetailsUpdatePayload = {
  __typename?: 'UserDetailsUpdatePayload';
  errors?: Maybe<Array<Maybe<UserDetailsUpdateError>>>;
  userDetails?: Maybe<UserDetails>;
};

export type UserDiscordSettings = {
  __typename?: 'UserDiscordSettings';
  guildId?: Maybe<Scalars['ID']['output']>;
  id: Scalars['ID']['output'];
};

export type UserImageInput = {
  height: Scalars['Int']['input'];
  url: Scalars['String']['input'];
  width: Scalars['Int']['input'];
};

export enum UserProfileType {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC'
}

export enum UserRole {
  MODERATOR = 'MODERATOR',
  USER = 'USER'
}

/** Represents spotify feed and connection details */
export type UserSpotifyDetails = {
  __typename?: 'UserSpotifyDetails';
  isConnected: Scalars['Boolean']['output'];
  podcastUri?: Maybe<Scalars['String']['output']>;
};

/** User can be either active or deleted. */
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  DELETED = 'DELETED'
}

/** Must be called UserSubscription since Subscription is root in GraphQL. */
export type UserSubscription = {
  creator: User;
  id: Scalars['ID']['output'];
  subscribedAt: Scalars['DateTime']['output'];
  subscriber: User;
};

/** Represents detail information about subscription, is available only to subscriber or creator of the subscription. */
export type UserSubscriptionDetails = UserSubscription & {
  __typename?: 'UserSubscriptionDetails';
  /** Will be populated if the subscription was purchased via Apple, the priceCents is an actual amount paid */
  applePaidPrice?: Maybe<Price>;
  cancelAtPeriodEnd: Scalars['Boolean']['output'];
  couponAppliedForDays?: Maybe<Scalars['Int']['output']>;
  /** If couponMethod or couponMethod are not null, and this field is null, then the coupon is applied forever */
  couponAppliedForMonths?: Maybe<Scalars['Int']['output']>;
  couponExpiresAt?: Maybe<Scalars['DateTime']['output']>;
  couponMethod?: Maybe<SubscriptionCouponMethod>;
  couponPercentOff?: Maybe<Scalars['Int']['output']>;
  creator: User;
  expires?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** @deprecated use applePrice instead */
  isAppleSubscription: Scalars['Boolean']['output'];
  status: SubscriptionStatus;
  subscribedAt: Scalars['DateTime']['output'];
  subscriber: User;
  /** Is null if `type` field is `INVITE` */
  tier?: Maybe<Tier>;
  type: SubscriptionType;
};

/** Represents general information about subscription, publicly available. */
export type UserSubscriptionInfo = UserSubscription & {
  __typename?: 'UserSubscriptionInfo';
  creator: User;
  id: Scalars['ID']['output'];
  subscribedAt: Scalars['DateTime']['output'];
  subscriber: User;
};

export type WelcomeMessage = {
  __typename?: 'WelcomeMessage';
  assets: Array<MessageAsset>;
  text: Scalars['String']['output'];
};

export type WelcomeMessageUpdateInput = {
  assets?: InputMaybe<Array<MessageAssetInput>>;
  text?: InputMaybe<Scalars['String']['input']>;
};

export type WelcomeMessageUpdatePayload = {
  __typename?: 'WelcomeMessageUpdatePayload';
  success: Scalars['Boolean']['output'];
  welcomeMessage?: Maybe<WelcomeMessage>;
};



export type ResolverTypeWrapper<T> = Promise<T> | T;

export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> = ResolverFn<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = {}, TContext = {}, TArgs = {}> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;



/** Mapping between all available schema types and the resolvers types */
export type ResolversTypes = {
  BigInt: ResolverTypeWrapper<Scalars['BigInt']['output']>;
  Boolean: ResolverTypeWrapper<Scalars['Boolean']['output']>;
  CategoriesOrderInput: CategoriesOrderInput;
  Category: ResolverTypeWrapper<Category>;
  CategoryCreateInput: CategoryCreateInput;
  CategoryPayload: ResolverTypeWrapper<CategoryPayload>;
  CategoryUpdateInput: CategoryUpdateInput;
  Comment: ResolverTypeWrapper<CommentModel>;
  CommentAttributesInput: CommentAttributesInput;
  CommentConnection: ResolverTypeWrapper<Omit<CommentConnection, 'nodes'> & { nodes: Array<ResolversTypes['Comment']> }>;
  CommentCreatePayload: ResolverTypeWrapper<Omit<CommentCreatePayload, 'comment'> & { comment?: Maybe<ResolversTypes['Comment']> }>;
  CommentNotification: ResolverTypeWrapper<NotificationModel>;
  CommentNotificationType: CommentNotificationType;
  CommentParent: ResolverTypeWrapper<ParentModel>;
  CommentUpdatePayload: ResolverTypeWrapper<Omit<CommentUpdatePayload, 'comment'> & { comment?: Maybe<ResolversTypes['Comment']> }>;
  Community: ResolverTypeWrapper<CommunityModel>;
  CommunityCommentNotification: ResolverTypeWrapper<NotificationModel>;
  CommunityCommentNotificationType: CommunityCommentNotificationType;
  CommunityConnection: ResolverTypeWrapper<Omit<CommunityConnection, 'nodes'> & { nodes: Array<ResolversTypes['Community']> }>;
  CommunityCreatePayload: ResolverTypeWrapper<Omit<CommunityCreatePayload, 'community'> & { community?: Maybe<ResolversTypes['Community']> }>;
  CommunityFilter: CommunityFilter;
  CommunitySort: CommunitySort;
  CommunitySortFields: CommunitySortFields;
  CommunityThreadNotification: ResolverTypeWrapper<NotificationModel>;
  CommunityThreadNotificationType: CommunityThreadNotificationType;
  CommunityType: CommunityType;
  CommunityUpdateInput: CommunityUpdateInput;
  CommunityUpdatePayload: ResolverTypeWrapper<Omit<CommunityUpdatePayload, 'community'> & { community?: Maybe<ResolversTypes['Community']> }>;
  CompleteContentPost: ResolverTypeWrapper<PostModel>;
  CreatorDetails: ResolverTypeWrapper<CreatorDetails>;
  Currency: Currency;
  DateTime: ResolverTypeWrapper<Scalars['DateTime']['output']>;
  ExpectedIncome: ResolverTypeWrapper<ExpectedIncomeModel>;
  FeaturedCategories: FeaturedCategories;
  Float: ResolverTypeWrapper<Scalars['Float']['output']>;
  GenericMutationPayload: ResolverTypeWrapper<GenericMutationPayload>;
  GenericNotification: ResolverTypeWrapper<GenericNotification>;
  GjirafaAssetQuality: ResolverTypeWrapper<GjirafaAssetQuality>;
  GjirafaLivestreamStatus: GjirafaLivestreamStatus;
  GjirafaQualityTypeStatus: GjirafaQualityTypeStatus;
  ID: ResolverTypeWrapper<Scalars['ID']['output']>;
  ImageAsset: ResolverTypeWrapper<ImageAsset>;
  ImageInput: ImageInput;
  Int: ResolverTypeWrapper<Scalars['Int']['output']>;
  LimitedContentPost: ResolverTypeWrapper<PostModel>;
  LivestreamDetails: ResolverTypeWrapper<LivestreamDetailsModel>;
  Message: ResolverTypeWrapper<MessageModel>;
  MessageAsset: ResolverTypeWrapper<MessageAssetModel>;
  MessageAssetInput: MessageAssetInput;
  MessageConnection: ResolverTypeWrapper<Omit<MessageConnection, 'nodes'> & { nodes: Array<ResolversTypes['Message']> }>;
  MessageCreateInput: MessageCreateInput;
  MessageCreatePayload: ResolverTypeWrapper<Omit<MessageCreatePayload, 'message'> & { message?: Maybe<ResolversTypes['Message']> }>;
  MessageLockedAsset: ResolverTypeWrapper<MessageLockedAsset>;
  MessageNotification: ResolverTypeWrapper<NotificationModel>;
  MessageNotificationType: MessageNotificationType;
  MessageThread: ResolverTypeWrapper<MessageThreadModel>;
  MessageThreadConnection: ResolverTypeWrapper<Omit<MessageThreadConnection, 'nodes'> & { nodes: Array<ResolversTypes['MessageThread']> }>;
  MessageThreadUpdateInput: MessageThreadUpdateInput;
  MessageThreadUpdatePayload: ResolverTypeWrapper<MessageThreadUpdatePayload>;
  MessageThreadUpsertInput: MessageThreadUpsertInput;
  MessageThreadUpsertPayload: ResolverTypeWrapper<Omit<MessageThreadUpsertPayload, 'messageThread'> & { messageThread?: Maybe<ResolversTypes['MessageThread']> }>;
  Mutation: ResolverTypeWrapper<{}>;
  Notification: ResolverTypeWrapper<NotificationModel>;
  NotificationConnection: ResolverTypeWrapper<Omit<NotificationConnection, 'nodes'> & { nodes: Array<ResolversTypes['Notification']> }>;
  NotificationFilter: NotificationFilter;
  NotificationSettings: ResolverTypeWrapper<NotificationSettings>;
  NotificationSettingsUpdateInput: NotificationSettingsUpdateInput;
  NotificationSettingsUpdatePayload: ResolverTypeWrapper<NotificationSettingsUpdatePayload>;
  NotificationTypeCategory: NotificationTypeCategory;
  NotificationUpdateInput: NotificationUpdateInput;
  PageInfo: ResolverTypeWrapper<PageInfo>;
  Poll: ResolverTypeWrapper<PollModel>;
  PollCastVotesInput: PollCastVotesInput;
  PollOption: ResolverTypeWrapper<PollOption>;
  Post: ResolverTypeWrapper<PostModel>;
  PostAddToLibraryPayload: ResolverTypeWrapper<Omit<PostAddToLibraryPayload, 'savedPost'> & { savedPost?: Maybe<ResolversTypes['SavedPost']> }>;
  PostAsset: ResolverTypeWrapper<PostAssetModel>;
  PostAssetInput: PostAssetInput;
  PostBunnyAsset: ResolverTypeWrapper<PostBunnyAsset>;
  PostCastVote: PostCastVote;
  PostConnection: ResolverTypeWrapper<Omit<PostConnection, 'nodes'> & { nodes: Array<ResolversTypes['Post']> }>;
  PostCounts: ResolverTypeWrapper<PostCounts>;
  PostCreateInput: PostCreateInput;
  PostCreatePayload: ResolverTypeWrapper<Omit<PostCreatePayload, 'post'> & { post?: Maybe<ResolversTypes['CompleteContentPost']> }>;
  PostDocumentAsset: ResolverTypeWrapper<PostDocumentAsset>;
  PostDocumentAssetInput: PostDocumentAssetInput;
  PostDocumentType: PostDocumentType;
  PostEmptyAsset: ResolverTypeWrapper<PostEmptyAsset>;
  PostFilter: PostFilter;
  PostGjirafaAsset: ResolverTypeWrapper<PostGjirafaAsset>;
  PostGjirafaAssetInput: PostGjirafaAssetInput;
  PostGjirafaLivestreamAsset: ResolverTypeWrapper<PostGjirafaLivestreamAsset>;
  PostGjirafaLivestreamAssetInput: PostGjirafaLivestreamAssetInput;
  PostImageAsset: ResolverTypeWrapper<PostImageAsset>;
  PostImageAssetInput: PostImageAssetInput;
  PostNotification: ResolverTypeWrapper<NotificationModel>;
  PostNotificationType: PostNotificationType;
  PostPreviewAsset: ResolverTypeWrapper<PreviewAssetModel>;
  PostPreviewDocumentAsset: ResolverTypeWrapper<PostPreviewDocumentAsset>;
  PostPreviewGjirafaAsset: ResolverTypeWrapper<PostPreviewGjirafaAsset>;
  PostPreviewGjirafaLiveAsset: ResolverTypeWrapper<PostPreviewGjirafaLiveAsset>;
  PostPreviewGjirafaType: PostPreviewGjirafaType;
  PostPreviewImageAsset: ResolverTypeWrapper<PostPreviewImageAsset>;
  PostSort: PostSort;
  PostSortFields: PostSortFields;
  PostSource: PostSource;
  PostState: PostState;
  PostTypeFilter: PostTypeFilter;
  PostUpdateInput: PostUpdateInput;
  PostUpdatePayload: ResolverTypeWrapper<Omit<PostUpdatePayload, 'post'> & { post?: Maybe<ResolversTypes['CompleteContentPost']> }>;
  PostVoteType: PostVoteType;
  PostYoutubeAsset: ResolverTypeWrapper<PostYoutubeAsset>;
  PreviewContentPost: ResolverTypeWrapper<PostModel>;
  Price: ResolverTypeWrapper<Price>;
  Query: ResolverTypeWrapper<{}>;
  RandomizedFeaturedCreatorsPayload: ResolverTypeWrapper<Omit<RandomizedFeaturedCreatorsPayload, 'users'> & { users: Array<ResolversTypes['User']> }>;
  RequestNotification: ResolverTypeWrapper<NotificationModel>;
  RequestNotificationType: RequestNotificationType;
  RssFeedUrl: ResolverTypeWrapper<RssFeedUrl>;
  RssFeedUrlGeneratePayload: ResolverTypeWrapper<RssFeedUrlGeneratePayload>;
  SavedPost: ResolverTypeWrapper<Omit<SavedPost, 'post'> & { post: ResolversTypes['Post'] }>;
  SavedPostConnection: ResolverTypeWrapper<Omit<SavedPostConnection, 'nodes'> & { nodes: Array<ResolversTypes['SavedPost']> }>;
  SavedPostInfo: ResolverTypeWrapper<SavedPostInfo>;
  SavedPostsFilter: SavedPostsFilter;
  SearchPostsFilter: SearchPostsFilter;
  SortDirection: SortDirection;
  String: ResolverTypeWrapper<Scalars['String']['output']>;
  StripeRequirements: ResolverTypeWrapper<StripeRequirements>;
  SubscribeRequest: ResolverTypeWrapper<SubscribeRequestModel>;
  SubscribeRequestConnection: ResolverTypeWrapper<Omit<SubscribeRequestConnection, 'nodes'> & { nodes: Array<ResolversTypes['SubscribeRequest']> }>;
  SubscribeRequestCreateInput: SubscribeRequestCreateInput;
  SubscribeRequestState: SubscribeRequestState;
  SubscriberNotification: ResolverTypeWrapper<NotificationModel>;
  SubscriberNotificationType: SubscriberNotificationType;
  SubscriptionConnection: ResolverTypeWrapper<Omit<SubscriptionConnection, 'nodes'> & { nodes: Array<ResolversTypes['UserSubscription']> }>;
  SubscriptionCouponMethod: SubscriptionCouponMethod;
  SubscriptionDeletePayload: ResolverTypeWrapper<Omit<SubscriptionDeletePayload, 'subscription'> & { subscription: ResolversTypes['UserSubscriptionDetails'] }>;
  SubscriptionFilter: SubscriptionFilter;
  SubscriptionNotification: ResolverTypeWrapper<NotificationModel>;
  SubscriptionNotificationType: SubscriptionNotificationType;
  SubscriptionOrderBy: SubscriptionOrderBy;
  SubscriptionRelationType: SubscriptionRelationType;
  SubscriptionRenewPayload: ResolverTypeWrapper<Omit<SubscriptionRenewPayload, 'subscription'> & { subscription: ResolversTypes['UserSubscriptionDetails'] }>;
  SubscriptionStatus: SubscriptionStatus;
  SubscriptionType: SubscriptionType;
  Tier: ResolverTypeWrapper<Tier>;
  User: ResolverTypeWrapper<UserModel>;
  UserAnalytics: ResolverTypeWrapper<UserAnalytics>;
  UserCompany: ResolverTypeWrapper<UserCompany>;
  UserCompanyVatType: UserCompanyVatType;
  UserConnection: ResolverTypeWrapper<Omit<UserConnection, 'nodes'> & { nodes: Array<ResolversTypes['User']> }>;
  UserCounts: ResolverTypeWrapper<UserCounts>;
  UserDetails: ResolverTypeWrapper<UserDetailsModel>;
  UserDetailsCounts: ResolverTypeWrapper<UserDetailsCounts>;
  UserDetailsUpdateError: ResolverTypeWrapper<UserDetailsUpdateError>;
  UserDetailsUpdateErrorType: UserDetailsUpdateErrorType;
  UserDetailsUpdateInput: UserDetailsUpdateInput;
  UserDetailsUpdatePayload: ResolverTypeWrapper<Omit<UserDetailsUpdatePayload, 'userDetails'> & { userDetails?: Maybe<ResolversTypes['UserDetails']> }>;
  UserDiscordSettings: ResolverTypeWrapper<UserDiscordSettings>;
  UserImageInput: UserImageInput;
  UserProfileType: UserProfileType;
  UserRole: UserRole;
  UserSpotifyDetails: ResolverTypeWrapper<UserSpotifyDetails>;
  UserStatus: UserStatus;
  UserSubscription: ResolverTypeWrapper<SubscriptionModel>;
  UserSubscriptionDetails: ResolverTypeWrapper<FullSubscriptionModel>;
  UserSubscriptionInfo: ResolverTypeWrapper<Omit<UserSubscriptionInfo, 'creator' | 'subscriber'> & { creator: ResolversTypes['User'], subscriber: ResolversTypes['User'] }>;
  Void: ResolverTypeWrapper<Scalars['Void']['output']>;
  WelcomeMessage: ResolverTypeWrapper<MessageModel>;
  WelcomeMessageUpdateInput: WelcomeMessageUpdateInput;
  WelcomeMessageUpdatePayload: ResolverTypeWrapper<Omit<WelcomeMessageUpdatePayload, 'welcomeMessage'> & { welcomeMessage?: Maybe<ResolversTypes['WelcomeMessage']> }>;
};

/** Mapping between all available schema types and the resolvers parents */
export type ResolversParentTypes = {
  BigInt: Scalars['BigInt']['output'];
  Boolean: Scalars['Boolean']['output'];
  CategoriesOrderInput: CategoriesOrderInput;
  Category: Category;
  CategoryCreateInput: CategoryCreateInput;
  CategoryPayload: CategoryPayload;
  CategoryUpdateInput: CategoryUpdateInput;
  Comment: CommentModel;
  CommentAttributesInput: CommentAttributesInput;
  CommentConnection: Omit<CommentConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['Comment']> };
  CommentCreatePayload: Omit<CommentCreatePayload, 'comment'> & { comment?: Maybe<ResolversParentTypes['Comment']> };
  CommentNotification: NotificationModel;
  CommentParent: ParentModel;
  CommentUpdatePayload: Omit<CommentUpdatePayload, 'comment'> & { comment?: Maybe<ResolversParentTypes['Comment']> };
  Community: CommunityModel;
  CommunityCommentNotification: NotificationModel;
  CommunityConnection: Omit<CommunityConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['Community']> };
  CommunityCreatePayload: Omit<CommunityCreatePayload, 'community'> & { community?: Maybe<ResolversParentTypes['Community']> };
  CommunityFilter: CommunityFilter;
  CommunitySort: CommunitySort;
  CommunityThreadNotification: NotificationModel;
  CommunityUpdateInput: CommunityUpdateInput;
  CommunityUpdatePayload: Omit<CommunityUpdatePayload, 'community'> & { community?: Maybe<ResolversParentTypes['Community']> };
  CompleteContentPost: PostModel;
  CreatorDetails: CreatorDetails;
  DateTime: Scalars['DateTime']['output'];
  ExpectedIncome: ExpectedIncomeModel;
  Float: Scalars['Float']['output'];
  GenericMutationPayload: GenericMutationPayload;
  GenericNotification: GenericNotification;
  GjirafaAssetQuality: GjirafaAssetQuality;
  ID: Scalars['ID']['output'];
  ImageAsset: ImageAsset;
  ImageInput: ImageInput;
  Int: Scalars['Int']['output'];
  LimitedContentPost: PostModel;
  LivestreamDetails: LivestreamDetailsModel;
  Message: MessageModel;
  MessageAsset: MessageAssetModel;
  MessageAssetInput: MessageAssetInput;
  MessageConnection: Omit<MessageConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['Message']> };
  MessageCreateInput: MessageCreateInput;
  MessageCreatePayload: Omit<MessageCreatePayload, 'message'> & { message?: Maybe<ResolversParentTypes['Message']> };
  MessageLockedAsset: MessageLockedAsset;
  MessageNotification: NotificationModel;
  MessageThread: MessageThreadModel;
  MessageThreadConnection: Omit<MessageThreadConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['MessageThread']> };
  MessageThreadUpdateInput: MessageThreadUpdateInput;
  MessageThreadUpdatePayload: MessageThreadUpdatePayload;
  MessageThreadUpsertInput: MessageThreadUpsertInput;
  MessageThreadUpsertPayload: Omit<MessageThreadUpsertPayload, 'messageThread'> & { messageThread?: Maybe<ResolversParentTypes['MessageThread']> };
  Mutation: {};
  Notification: NotificationModel;
  NotificationConnection: Omit<NotificationConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['Notification']> };
  NotificationFilter: NotificationFilter;
  NotificationSettings: NotificationSettings;
  NotificationSettingsUpdateInput: NotificationSettingsUpdateInput;
  NotificationSettingsUpdatePayload: NotificationSettingsUpdatePayload;
  NotificationUpdateInput: NotificationUpdateInput;
  PageInfo: PageInfo;
  Poll: PollModel;
  PollCastVotesInput: PollCastVotesInput;
  PollOption: PollOption;
  Post: PostModel;
  PostAddToLibraryPayload: Omit<PostAddToLibraryPayload, 'savedPost'> & { savedPost?: Maybe<ResolversParentTypes['SavedPost']> };
  PostAsset: PostAssetModel;
  PostAssetInput: PostAssetInput;
  PostBunnyAsset: PostBunnyAsset;
  PostCastVote: PostCastVote;
  PostConnection: Omit<PostConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['Post']> };
  PostCounts: PostCounts;
  PostCreateInput: PostCreateInput;
  PostCreatePayload: Omit<PostCreatePayload, 'post'> & { post?: Maybe<ResolversParentTypes['CompleteContentPost']> };
  PostDocumentAsset: PostDocumentAsset;
  PostDocumentAssetInput: PostDocumentAssetInput;
  PostEmptyAsset: PostEmptyAsset;
  PostFilter: PostFilter;
  PostGjirafaAsset: PostGjirafaAsset;
  PostGjirafaAssetInput: PostGjirafaAssetInput;
  PostGjirafaLivestreamAsset: PostGjirafaLivestreamAsset;
  PostGjirafaLivestreamAssetInput: PostGjirafaLivestreamAssetInput;
  PostImageAsset: PostImageAsset;
  PostImageAssetInput: PostImageAssetInput;
  PostNotification: NotificationModel;
  PostPreviewAsset: PreviewAssetModel;
  PostPreviewDocumentAsset: PostPreviewDocumentAsset;
  PostPreviewGjirafaAsset: PostPreviewGjirafaAsset;
  PostPreviewGjirafaLiveAsset: PostPreviewGjirafaLiveAsset;
  PostPreviewImageAsset: PostPreviewImageAsset;
  PostSort: PostSort;
  PostUpdateInput: PostUpdateInput;
  PostUpdatePayload: Omit<PostUpdatePayload, 'post'> & { post?: Maybe<ResolversParentTypes['CompleteContentPost']> };
  PostYoutubeAsset: PostYoutubeAsset;
  PreviewContentPost: PostModel;
  Price: Price;
  Query: {};
  RandomizedFeaturedCreatorsPayload: Omit<RandomizedFeaturedCreatorsPayload, 'users'> & { users: Array<ResolversParentTypes['User']> };
  RequestNotification: NotificationModel;
  RssFeedUrl: RssFeedUrl;
  RssFeedUrlGeneratePayload: RssFeedUrlGeneratePayload;
  SavedPost: Omit<SavedPost, 'post'> & { post: ResolversParentTypes['Post'] };
  SavedPostConnection: Omit<SavedPostConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['SavedPost']> };
  SavedPostInfo: SavedPostInfo;
  SavedPostsFilter: SavedPostsFilter;
  SearchPostsFilter: SearchPostsFilter;
  String: Scalars['String']['output'];
  StripeRequirements: StripeRequirements;
  SubscribeRequest: SubscribeRequestModel;
  SubscribeRequestConnection: Omit<SubscribeRequestConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['SubscribeRequest']> };
  SubscribeRequestCreateInput: SubscribeRequestCreateInput;
  SubscriberNotification: NotificationModel;
  SubscriptionConnection: Omit<SubscriptionConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['UserSubscription']> };
  SubscriptionDeletePayload: Omit<SubscriptionDeletePayload, 'subscription'> & { subscription: ResolversParentTypes['UserSubscriptionDetails'] };
  SubscriptionFilter: SubscriptionFilter;
  SubscriptionNotification: NotificationModel;
  SubscriptionRenewPayload: Omit<SubscriptionRenewPayload, 'subscription'> & { subscription: ResolversParentTypes['UserSubscriptionDetails'] };
  Tier: Tier;
  User: UserModel;
  UserAnalytics: UserAnalytics;
  UserCompany: UserCompany;
  UserConnection: Omit<UserConnection, 'nodes'> & { nodes: Array<ResolversParentTypes['User']> };
  UserCounts: UserCounts;
  UserDetails: UserDetailsModel;
  UserDetailsCounts: UserDetailsCounts;
  UserDetailsUpdateError: UserDetailsUpdateError;
  UserDetailsUpdateInput: UserDetailsUpdateInput;
  UserDetailsUpdatePayload: Omit<UserDetailsUpdatePayload, 'userDetails'> & { userDetails?: Maybe<ResolversParentTypes['UserDetails']> };
  UserDiscordSettings: UserDiscordSettings;
  UserImageInput: UserImageInput;
  UserSpotifyDetails: UserSpotifyDetails;
  UserSubscription: SubscriptionModel;
  UserSubscriptionDetails: FullSubscriptionModel;
  UserSubscriptionInfo: Omit<UserSubscriptionInfo, 'creator' | 'subscriber'> & { creator: ResolversParentTypes['User'], subscriber: ResolversParentTypes['User'] };
  Void: Scalars['Void']['output'];
  WelcomeMessage: MessageModel;
  WelcomeMessageUpdateInput: WelcomeMessageUpdateInput;
  WelcomeMessageUpdatePayload: Omit<WelcomeMessageUpdatePayload, 'welcomeMessage'> & { welcomeMessage?: Maybe<ResolversParentTypes['WelcomeMessage']> };
};

export interface BigIntScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['BigInt'], any> {
  name: 'BigInt';
}

export type CategoryResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Category'] = ResolversParentTypes['Category']> = {
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  slug?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CategoryPayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CategoryPayload'] = ResolversParentTypes['CategoryPayload']> = {
  category?: Resolver<ResolversTypes['Category'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommentResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Comment'] = ResolversParentTypes['Comment']> = {
  assets?: Resolver<Array<ResolversTypes['PostAsset']>, ParentType, ContextType>;
  counts?: Resolver<ResolversTypes['PostCounts'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  myVote?: Resolver<ResolversTypes['PostVoteType'], ParentType, ContextType>;
  parent?: Resolver<ResolversTypes['CommentParent'], ParentType, ContextType>;
  post?: Resolver<ResolversTypes['Post'], ParentType, ContextType>;
  publishedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  siblingId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  state?: Resolver<ResolversTypes['PostState'], ParentType, ContextType>;
  text?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  textDelta?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  textHtml?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  textMarkdown?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  user?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  voteScore?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommentConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommentConnection'] = ResolversParentTypes['CommentConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['Comment']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommentCreatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommentCreatePayload'] = ResolversParentTypes['CommentCreatePayload']> = {
  comment?: Resolver<Maybe<ResolversTypes['Comment']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommentNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommentNotification'] = ResolversParentTypes['CommentNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  commentId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  commenter?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  community?: Resolver<Maybe<ResolversTypes['Community']>, ParentType, ContextType>;
  communityId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  post?: Resolver<ResolversTypes['Post'], ParentType, ContextType>;
  postId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['CommentNotificationType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommentParentResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommentParent'] = ResolversParentTypes['CommentParent']> = {
  __resolveType: TypeResolveFn<'Comment' | 'CompleteContentPost' | 'LimitedContentPost', ParentType, ContextType>;
};

export type CommentUpdatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommentUpdatePayload'] = ResolversParentTypes['CommentUpdatePayload']> = {
  comment?: Resolver<Maybe<ResolversTypes['Comment']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommunityResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Community'] = ResolversParentTypes['Community']> = {
  description?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes['ImageAsset']>, ParentType, ContextType>;
  isMember?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  isVerified?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  membersCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  owner?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  slug?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  slugEditableAfter?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  threadsCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  type?: Resolver<ResolversTypes['CommunityType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommunityCommentNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommunityCommentNotification'] = ResolversParentTypes['CommunityCommentNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  commentId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  commenter?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  community?: Resolver<ResolversTypes['Community'], ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  threadId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  type?: Resolver<ResolversTypes['CommunityCommentNotificationType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommunityConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommunityConnection'] = ResolversParentTypes['CommunityConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['Community']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommunityCreatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommunityCreatePayload'] = ResolversParentTypes['CommunityCreatePayload']> = {
  community?: Resolver<Maybe<ResolversTypes['Community']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommunityThreadNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommunityThreadNotification'] = ResolversParentTypes['CommunityThreadNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  author?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  community?: Resolver<ResolversTypes['Community'], ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  threadId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  type?: Resolver<ResolversTypes['CommunityThreadNotificationType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CommunityUpdatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CommunityUpdatePayload'] = ResolversParentTypes['CommunityUpdatePayload']> = {
  community?: Resolver<Maybe<ResolversTypes['Community']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CompleteContentPostResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CompleteContentPost'] = ResolversParentTypes['CompleteContentPost']> = {
  assets?: Resolver<Array<ResolversTypes['PostAsset']>, ParentType, ContextType>;
  categories?: Resolver<Array<ResolversTypes['Category']>, ParentType, ContextType>;
  comments?: Resolver<ResolversTypes['CommentConnection'], ParentType, ContextType, Partial<CompleteContentPostCommentsArgs>>;
  community?: Resolver<Maybe<ResolversTypes['Community']>, ParentType, ContextType>;
  counts?: Resolver<ResolversTypes['PostCounts'], ParentType, ContextType>;
  hasPreview?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  isAgeRestricted?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  isExcludedFromRss?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  isSponsored?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  markdown?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  myVote?: Resolver<ResolversTypes['PostVoteType'], ParentType, ContextType>;
  pinnedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  poll?: Resolver<Maybe<ResolversTypes['Poll']>, ParentType, ContextType>;
  posts?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType, Partial<CompleteContentPostPostsArgs>>;
  publishedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  savedPostInfo?: Resolver<Maybe<ResolversTypes['SavedPostInfo']>, ParentType, ContextType>;
  state?: Resolver<ResolversTypes['PostState'], ParentType, ContextType>;
  text?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  textDelta?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  textHtml?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  title?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  user?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  voteScore?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CreatorDetailsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['CreatorDetails'] = ResolversParentTypes['CreatorDetails']> = {
  stripeAccountActive?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  stripeAccountId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  stripeAccountOnboarded?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  stripeRequirements?: Resolver<Maybe<ResolversTypes['StripeRequirements']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface DateTimeScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['DateTime'], any> {
  name: 'DateTime';
}

export type ExpectedIncomeResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['ExpectedIncome'] = ResolversParentTypes['ExpectedIncome']> = {
  grossIncomeCents?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  netIncomeCents?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GenericMutationPayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['GenericMutationPayload'] = ResolversParentTypes['GenericMutationPayload']> = {
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GenericNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['GenericNotification'] = ResolversParentTypes['GenericNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GjirafaAssetQualityResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['GjirafaAssetQuality'] = ResolversParentTypes['GjirafaAssetQuality']> = {
  duration?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  quality?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  size?: Resolver<ResolversTypes['BigInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ImageAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['ImageAsset'] = ResolversParentTypes['ImageAsset']> = {
  fileName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  fileSize?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  height?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  hidden?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  url?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  width?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type LimitedContentPostResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['LimitedContentPost'] = ResolversParentTypes['LimitedContentPost']> = {
  categories?: Resolver<Array<ResolversTypes['Category']>, ParentType, ContextType>;
  counts?: Resolver<ResolversTypes['PostCounts'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  pinnedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  posts?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType, Partial<LimitedContentPostPostsArgs>>;
  publishedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  savedPostInfo?: Resolver<Maybe<ResolversTypes['SavedPostInfo']>, ParentType, ContextType>;
  state?: Resolver<ResolversTypes['PostState'], ParentType, ContextType>;
  user?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type LivestreamDetailsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['LivestreamDetails'] = ResolversParentTypes['LivestreamDetails']> = {
  playbackUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  streamKey?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  streamUrl?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Message'] = ResolversParentTypes['Message']> = {
  assets?: Resolver<Array<ResolversTypes['MessageAsset']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  price?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  sentAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  sentBy?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  text?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageAsset'] = ResolversParentTypes['MessageAsset']> = {
  __resolveType: TypeResolveFn<'MessageLockedAsset' | 'PostDocumentAsset' | 'PostGjirafaAsset' | 'PostImageAsset', ParentType, ContextType>;
};

export type MessageConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageConnection'] = ResolversParentTypes['MessageConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['Message']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageCreatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageCreatePayload'] = ResolversParentTypes['MessageCreatePayload']> = {
  message?: Resolver<Maybe<ResolversTypes['Message']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageLockedAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageLockedAsset'] = ResolversParentTypes['MessageLockedAsset']> = {
  price?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageNotification'] = ResolversParentTypes['MessageNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  message?: Resolver<Maybe<ResolversTypes['Message']>, ParentType, ContextType>;
  messageId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  messageThreadId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['MessageNotificationType'], ParentType, ContextType>;
  user?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageThreadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageThread'] = ResolversParentTypes['MessageThread']> = {
  canMessage?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  lastMessage?: Resolver<Maybe<ResolversTypes['Message']>, ParentType, ContextType>;
  participants?: Resolver<Array<ResolversTypes['User']>, ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageThreadConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageThreadConnection'] = ResolversParentTypes['MessageThreadConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['MessageThread']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageThreadUpdatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageThreadUpdatePayload'] = ResolversParentTypes['MessageThreadUpdatePayload']> = {
  success?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MessageThreadUpsertPayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['MessageThreadUpsertPayload'] = ResolversParentTypes['MessageThreadUpsertPayload']> = {
  messageThread?: Resolver<Maybe<ResolversTypes['MessageThread']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MutationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Mutation'] = ResolversParentTypes['Mutation']> = {
  assetTimestampUpdate?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationAssetTimestampUpdateArgs, 'assetId'>>;
  categoriesOrder?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationCategoriesOrderArgs, 'input'>>;
  categoryCreate?: Resolver<ResolversTypes['CategoryPayload'], ParentType, ContextType, RequireFields<MutationCategoryCreateArgs, 'input'>>;
  categoryDelete?: Resolver<ResolversTypes['GenericMutationPayload'], ParentType, ContextType, RequireFields<MutationCategoryDeleteArgs, 'categoryId'>>;
  categoryUpdate?: Resolver<ResolversTypes['CategoryPayload'], ParentType, ContextType, RequireFields<MutationCategoryUpdateArgs, 'id' | 'input'>>;
  commentCreate?: Resolver<ResolversTypes['CommentCreatePayload'], ParentType, ContextType, RequireFields<MutationCommentCreateArgs, 'attributes' | 'parentId'>>;
  commentDelete?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationCommentDeleteArgs, 'commentId'>>;
  commentUpdate?: Resolver<ResolversTypes['CommentUpdatePayload'], ParentType, ContextType, RequireFields<MutationCommentUpdateArgs, 'attributes' | 'commentId'>>;
  communityCreate?: Resolver<ResolversTypes['CommunityCreatePayload'], ParentType, ContextType>;
  communityJoin?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationCommunityJoinArgs, 'communityId'>>;
  communityLeave?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationCommunityLeaveArgs, 'communityId'>>;
  communityUpdate?: Resolver<ResolversTypes['CommunityUpdatePayload'], ParentType, ContextType, RequireFields<MutationCommunityUpdateArgs, 'communityId' | 'input'>>;
  messageCreate?: Resolver<ResolversTypes['MessageCreatePayload'], ParentType, ContextType, RequireFields<MutationMessageCreateArgs, 'input' | 'messageThreadId'>>;
  messageThreadMarkAllSeen?: Resolver<ResolversTypes['GenericMutationPayload'], ParentType, ContextType>;
  messageThreadUpdate?: Resolver<ResolversTypes['MessageThreadUpdatePayload'], ParentType, ContextType, RequireFields<MutationMessageThreadUpdateArgs, 'input' | 'messageThreadId'>>;
  messageThreadUpsert?: Resolver<ResolversTypes['MessageThreadUpsertPayload'], ParentType, ContextType, RequireFields<MutationMessageThreadUpsertArgs, 'input'>>;
  notificationMarkAllSeen?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType>;
  notificationSettingsUpdate?: Resolver<ResolversTypes['NotificationSettingsUpdatePayload'], ParentType, ContextType, RequireFields<MutationNotificationSettingsUpdateArgs, 'input'>>;
  notificationUpdate?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationNotificationUpdateArgs, 'id' | 'input'>>;
  pollCastVotes?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationPollCastVotesArgs, 'input' | 'pollId'>>;
  pollEnd?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationPollEndArgs, 'pollId'>>;
  postAddToLibrary?: Resolver<ResolversTypes['PostAddToLibraryPayload'], ParentType, ContextType, RequireFields<MutationPostAddToLibraryArgs, 'postId'>>;
  postCastVote?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationPostCastVoteArgs, 'input' | 'postId'>>;
  postCreate?: Resolver<ResolversTypes['PostCreatePayload'], ParentType, ContextType, RequireFields<MutationPostCreateArgs, 'attributes'>>;
  postDelete?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationPostDeleteArgs, 'postId'>>;
  postRemoveFromLibrary?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationPostRemoveFromLibraryArgs, 'postId'>>;
  postUpdate?: Resolver<ResolversTypes['PostUpdatePayload'], ParentType, ContextType, RequireFields<MutationPostUpdateArgs, 'attributes' | 'postId'>>;
  rssFeedUrlGenerate?: Resolver<ResolversTypes['RssFeedUrlGeneratePayload'], ParentType, ContextType, RequireFields<MutationRssFeedUrlGenerateArgs, 'creatorId'>>;
  sessionRevoke?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType>;
  subscribeRequestAccept?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationSubscribeRequestAcceptArgs, 'id'>>;
  subscribeRequestAcceptAll?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType>;
  subscribeRequestCancel?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationSubscribeRequestCancelArgs, 'creatorId'>>;
  subscribeRequestCreate?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationSubscribeRequestCreateArgs, 'input'>>;
  subscribeRequestDecline?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType, RequireFields<MutationSubscribeRequestDeclineArgs, 'id'>>;
  subscribeRequestMarkAllSeen?: Resolver<Maybe<ResolversTypes['GenericMutationPayload']>, ParentType, ContextType>;
  subscriberDelete?: Resolver<ResolversTypes['SubscriptionDeletePayload'], ParentType, ContextType, RequireFields<MutationSubscriberDeleteArgs, 'subscriberId'>>;
  subscriptionCancel?: Resolver<ResolversTypes['SubscriptionDeletePayload'], ParentType, ContextType, RequireFields<MutationSubscriptionCancelArgs, 'creatorId'>>;
  subscriptionRenew?: Resolver<ResolversTypes['SubscriptionRenewPayload'], ParentType, ContextType, RequireFields<MutationSubscriptionRenewArgs, 'creatorId'>>;
  userDetailsUpdate?: Resolver<ResolversTypes['UserDetailsUpdatePayload'], ParentType, ContextType, RequireFields<MutationUserDetailsUpdateArgs, 'userDetails'>>;
  viewerUpdate?: Resolver<ResolversTypes['UserDetailsUpdatePayload'], ParentType, ContextType, RequireFields<MutationViewerUpdateArgs, 'userDetails'>>;
  welcomeMessageUpdate?: Resolver<ResolversTypes['WelcomeMessageUpdatePayload'], ParentType, ContextType, RequireFields<MutationWelcomeMessageUpdateArgs, 'input'>>;
};

export type NotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Notification'] = ResolversParentTypes['Notification']> = {
  __resolveType: TypeResolveFn<'CommentNotification' | 'CommunityCommentNotification' | 'CommunityThreadNotification' | 'GenericNotification' | 'MessageNotification' | 'PostNotification' | 'RequestNotification' | 'SubscriberNotification' | 'SubscriptionNotification', ParentType, ContextType>;
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
};

export type NotificationConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['NotificationConnection'] = ResolversParentTypes['NotificationConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['Notification']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type NotificationSettingsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['NotificationSettings'] = ResolversParentTypes['NotificationSettings']> = {
  emailNewDm?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  emailNewPost?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  newsletter?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  pushNewComment?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  pushNewMessage?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  pushNewPost?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  termsChanged?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type NotificationSettingsUpdatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['NotificationSettingsUpdatePayload'] = ResolversParentTypes['NotificationSettingsUpdatePayload']> = {
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PageInfoResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PageInfo'] = ResolversParentTypes['PageInfo']> = {
  endCursor?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  hasNextPage?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  startCursor?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PollResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Poll'] = ResolversParentTypes['Poll']> = {
  deadline?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  options?: Resolver<Array<ResolversTypes['PollOption']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PollOptionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PollOption'] = ResolversParentTypes['PollOption']> = {
  hasVotedFor?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  title?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  voteCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Post'] = ResolversParentTypes['Post']> = {
  __resolveType: TypeResolveFn<'CompleteContentPost' | 'LimitedContentPost' | 'PreviewContentPost', ParentType, ContextType>;
  categories?: Resolver<Array<ResolversTypes['Category']>, ParentType, ContextType>;
  counts?: Resolver<ResolversTypes['PostCounts'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  pinnedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  posts?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType, Partial<PostPostsArgs>>;
  publishedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  savedPostInfo?: Resolver<Maybe<ResolversTypes['SavedPostInfo']>, ParentType, ContextType>;
  state?: Resolver<ResolversTypes['PostState'], ParentType, ContextType>;
  user?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
};

export type PostAddToLibraryPayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostAddToLibraryPayload'] = ResolversParentTypes['PostAddToLibraryPayload']> = {
  savedPost?: Resolver<Maybe<ResolversTypes['SavedPost']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostAsset'] = ResolversParentTypes['PostAsset']> = {
  __resolveType: TypeResolveFn<'PostBunnyAsset' | 'PostDocumentAsset' | 'PostEmptyAsset' | 'PostGjirafaAsset' | 'PostGjirafaLivestreamAsset' | 'PostImageAsset' | 'PostYoutubeAsset', ParentType, ContextType>;
};

export type PostBunnyAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostBunnyAsset'] = ResolversParentTypes['PostBunnyAsset']> = {
  url?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostConnection'] = ResolversParentTypes['PostConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['Post']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostCountsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostCounts'] = ResolversParentTypes['PostCounts']> = {
  comments?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  replies?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostCreatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostCreatePayload'] = ResolversParentTypes['PostCreatePayload']> = {
  post?: Resolver<Maybe<ResolversTypes['CompleteContentPost']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostDocumentAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostDocumentAsset'] = ResolversParentTypes['PostDocumentAsset']> = {
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  thumbnail?: Resolver<Maybe<ResolversTypes['PostImageAsset']>, ParentType, ContextType>;
  thumbnailUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['PostDocumentType'], ParentType, ContextType>;
  url?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostEmptyAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostEmptyAsset'] = ResolversParentTypes['PostEmptyAsset']> = {
  dummy?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostGjirafaAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostGjirafaAsset'] = ResolversParentTypes['PostGjirafaAsset']> = {
  audioByteSize?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  audioStaticUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  audioStreamUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  duration?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  gjirafaId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  hasAudio?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  hasVideo?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  height?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  hidden?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  isLivestreamRecording?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  key?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  keyId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  previewAnimatedUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  previewStaticUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  previewStripUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  progressTillCompleteness?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  progressTillReadiness?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  status?: Resolver<ResolversTypes['GjirafaQualityTypeStatus'], ParentType, ContextType>;
  thumbnail?: Resolver<Maybe<ResolversTypes['PostImageAsset']>, ParentType, ContextType>;
  thumbnailUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  timestamp?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  videoStreamUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  width?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostGjirafaLivestreamAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostGjirafaLivestreamAsset'] = ResolversParentTypes['PostGjirafaLivestreamAsset']> = {
  channelPublicId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  gjirafaId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  playbackUrl?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  startDateUTC?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  status?: Resolver<ResolversTypes['GjirafaLivestreamStatus'], ParentType, ContextType>;
  thumbnail?: Resolver<Maybe<ResolversTypes['PostImageAsset']>, ParentType, ContextType>;
  thumbnailUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostImageAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostImageAsset'] = ResolversParentTypes['PostImageAsset']> = {
  height?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  url?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  width?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostNotification'] = ResolversParentTypes['PostNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  community?: Resolver<Maybe<ResolversTypes['Community']>, ParentType, ContextType>;
  communityId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  creator?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  post?: Resolver<Maybe<ResolversTypes['Post']>, ParentType, ContextType>;
  postId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['PostNotificationType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostPreviewAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostPreviewAsset'] = ResolversParentTypes['PostPreviewAsset']> = {
  __resolveType: TypeResolveFn<'PostPreviewDocumentAsset' | 'PostPreviewGjirafaAsset' | 'PostPreviewGjirafaLiveAsset' | 'PostPreviewImageAsset', ParentType, ContextType>;
};

export type PostPreviewDocumentAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostPreviewDocumentAsset'] = ResolversParentTypes['PostPreviewDocumentAsset']> = {
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  thumbnailUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['PostDocumentType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostPreviewGjirafaAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostPreviewGjirafaAsset'] = ResolversParentTypes['PostPreviewGjirafaAsset']> = {
  duration?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  height?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  previewAnimatedUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  previewStaticUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  thumbnailUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['PostPreviewGjirafaType'], ParentType, ContextType>;
  width?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostPreviewGjirafaLiveAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostPreviewGjirafaLiveAsset'] = ResolversParentTypes['PostPreviewGjirafaLiveAsset']> = {
  thumbnailUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostPreviewImageAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostPreviewImageAsset'] = ResolversParentTypes['PostPreviewImageAsset']> = {
  height?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  url?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  width?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostUpdatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostUpdatePayload'] = ResolversParentTypes['PostUpdatePayload']> = {
  post?: Resolver<Maybe<ResolversTypes['CompleteContentPost']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PostYoutubeAssetResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PostYoutubeAsset'] = ResolversParentTypes['PostYoutubeAsset']> = {
  height?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  previewUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  thumbnail?: Resolver<Maybe<ResolversTypes['PostImageAsset']>, ParentType, ContextType>;
  thumbnailUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  width?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PreviewContentPostResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['PreviewContentPost'] = ResolversParentTypes['PreviewContentPost']> = {
  assets?: Resolver<Array<ResolversTypes['PostPreviewAsset']>, ParentType, ContextType>;
  categories?: Resolver<Array<ResolversTypes['Category']>, ParentType, ContextType>;
  counts?: Resolver<ResolversTypes['PostCounts'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  pinnedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  poll?: Resolver<Maybe<ResolversTypes['Poll']>, ParentType, ContextType>;
  posts?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType, Partial<PreviewContentPostPostsArgs>>;
  publishedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  savedPostInfo?: Resolver<Maybe<ResolversTypes['SavedPostInfo']>, ParentType, ContextType>;
  state?: Resolver<ResolversTypes['PostState'], ParentType, ContextType>;
  text?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  textHtml?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  title?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  user?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PriceResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Price'] = ResolversParentTypes['Price']> = {
  currency?: Resolver<ResolversTypes['Currency'], ParentType, ContextType>;
  priceCents?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type QueryResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Query'] = ResolversParentTypes['Query']> = {
  adminUserDetails?: Resolver<ResolversTypes['UserDetails'], ParentType, ContextType, RequireFields<QueryAdminUserDetailsArgs, 'id'>>;
  comment?: Resolver<ResolversTypes['Comment'], ParentType, ContextType, RequireFields<QueryCommentArgs, 'id'>>;
  comments?: Resolver<ResolversTypes['CommentConnection'], ParentType, ContextType, RequireFields<QueryCommentsArgs, 'id'>>;
  communities?: Resolver<ResolversTypes['CommunityConnection'], ParentType, ContextType, Partial<QueryCommunitiesArgs>>;
  community?: Resolver<ResolversTypes['Community'], ParentType, ContextType, Partial<QueryCommunityArgs>>;
  expectedIncome?: Resolver<ResolversTypes['ExpectedIncome'], ParentType, ContextType>;
  featuredCreators?: Resolver<ResolversTypes['UserConnection'], ParentType, ContextType, Partial<QueryFeaturedCreatorsArgs>>;
  featuredCreatorsRandomized?: Resolver<ResolversTypes['RandomizedFeaturedCreatorsPayload'], ParentType, ContextType, RequireFields<QueryFeaturedCreatorsRandomizedArgs, 'featuredCategory'>>;
  featuredPosts?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType, Partial<QueryFeaturedPostsArgs>>;
  featuredThreads?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType, Partial<QueryFeaturedThreadsArgs>>;
  gjirafaVideoQualities?: Resolver<Array<ResolversTypes['GjirafaAssetQuality']>, ParentType, ContextType, RequireFields<QueryGjirafaVideoQualitiesArgs, 'assetId'>>;
  livestreams?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType>;
  messageThread?: Resolver<ResolversTypes['MessageThread'], ParentType, ContextType, RequireFields<QueryMessageThreadArgs, 'messageThreadId'>>;
  messageThreads?: Resolver<ResolversTypes['MessageThreadConnection'], ParentType, ContextType, Partial<QueryMessageThreadsArgs>>;
  messages?: Resolver<ResolversTypes['MessageConnection'], ParentType, ContextType, RequireFields<QueryMessagesArgs, 'messageThreadId'>>;
  notificationSettings?: Resolver<ResolversTypes['NotificationSettings'], ParentType, ContextType>;
  notifications?: Resolver<ResolversTypes['NotificationConnection'], ParentType, ContextType, Partial<QueryNotificationsArgs>>;
  post?: Resolver<ResolversTypes['Post'], ParentType, ContextType, RequireFields<QueryPostArgs, 'id'>>;
  posts?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType, RequireFields<QueryPostsArgs, 'reverse'>>;
  savedPosts?: Resolver<ResolversTypes['SavedPostConnection'], ParentType, ContextType, Partial<QuerySavedPostsArgs>>;
  searchCommunities?: Resolver<ResolversTypes['CommunityConnection'], ParentType, ContextType, RequireFields<QuerySearchCommunitiesArgs, 'query'>>;
  searchPosts?: Resolver<ResolversTypes['PostConnection'], ParentType, ContextType, Partial<QuerySearchPostsArgs>>;
  searchUsers?: Resolver<ResolversTypes['UserConnection'], ParentType, ContextType, RequireFields<QuerySearchUsersArgs, 'query'>>;
  subscribeRequests?: Resolver<ResolversTypes['SubscribeRequestConnection'], ParentType, ContextType, Partial<QuerySubscribeRequestsArgs>>;
  subscribers?: Resolver<ResolversTypes['SubscriptionConnection'], ParentType, ContextType, RequireFields<QuerySubscribersArgs, 'creatorId' | 'orderBy'>>;
  subscriptions?: Resolver<ResolversTypes['SubscriptionConnection'], ParentType, ContextType, RequireFields<QuerySubscriptionsArgs, 'orderBy' | 'userId'>>;
  user?: Resolver<ResolversTypes['User'], ParentType, ContextType, RequireFields<QueryUserArgs, 'id'>>;
  viewer?: Resolver<ResolversTypes['UserDetails'], ParentType, ContextType>;
  viewerSubscription?: Resolver<Maybe<ResolversTypes['UserSubscriptionDetails']>, ParentType, ContextType, RequireFields<QueryViewerSubscriptionArgs, 'creatorId'>>;
  welcomeMessage?: Resolver<ResolversTypes['WelcomeMessage'], ParentType, ContextType>;
};

export type RandomizedFeaturedCreatorsPayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['RandomizedFeaturedCreatorsPayload'] = ResolversParentTypes['RandomizedFeaturedCreatorsPayload']> = {
  users?: Resolver<Array<ResolversTypes['User']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type RequestNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['RequestNotification'] = ResolversParentTypes['RequestNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  creator?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['RequestNotificationType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type RssFeedUrlResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['RssFeedUrl'] = ResolversParentTypes['RssFeedUrl']> = {
  url?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type RssFeedUrlGeneratePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['RssFeedUrlGeneratePayload'] = ResolversParentTypes['RssFeedUrlGeneratePayload']> = {
  rssFeedUrl?: Resolver<Maybe<ResolversTypes['RssFeedUrl']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SavedPostResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SavedPost'] = ResolversParentTypes['SavedPost']> = {
  post?: Resolver<ResolversTypes['Post'], ParentType, ContextType>;
  savedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SavedPostConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SavedPostConnection'] = ResolversParentTypes['SavedPostConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['SavedPost']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SavedPostInfoResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SavedPostInfo'] = ResolversParentTypes['SavedPostInfo']> = {
  savedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type StripeRequirementsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['StripeRequirements'] = ResolversParentTypes['StripeRequirements']> = {
  currentlyDue?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  deleted?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  disabledReason?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  errors?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  eventuallyDue?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  pastDue?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  pendingVerification?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  stripeAccountId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  valid?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SubscribeRequestResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SubscribeRequest'] = ResolversParentTypes['SubscribeRequest']> = {
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  user?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SubscribeRequestConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SubscribeRequestConnection'] = ResolversParentTypes['SubscribeRequestConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['SubscribeRequest']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SubscriberNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SubscriberNotification'] = ResolversParentTypes['SubscriberNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  lastSubscriber?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['SubscriberNotificationType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SubscriptionConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SubscriptionConnection'] = ResolversParentTypes['SubscriptionConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['UserSubscription']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SubscriptionDeletePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SubscriptionDeletePayload'] = ResolversParentTypes['SubscriptionDeletePayload']> = {
  subscription?: Resolver<ResolversTypes['UserSubscriptionDetails'], ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SubscriptionNotificationResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SubscriptionNotification'] = ResolversParentTypes['SubscriptionNotification']> = {
  actorCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  checkedAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  createdAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  creator?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  seenAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  targetId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['SubscriptionNotificationType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SubscriptionRenewPayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['SubscriptionRenewPayload'] = ResolversParentTypes['SubscriptionRenewPayload']> = {
  subscription?: Resolver<ResolversTypes['UserSubscriptionDetails'], ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TierResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['Tier'] = ResolversParentTypes['Tier']> = {
  currency?: Resolver<ResolversTypes['Currency'], ParentType, ContextType>;
  default?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  hidden?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  priceCents?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['User'] = ResolversParentTypes['User']> = {
  analytics?: Resolver<ResolversTypes['UserAnalytics'], ParentType, ContextType>;
  bio?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  bioMarkdown?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  categories?: Resolver<Array<ResolversTypes['Category']>, ParentType, ContextType>;
  counts?: Resolver<ResolversTypes['UserCounts'], ParentType, ContextType>;
  emailPublic?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  hasGiftsAllowed?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  hasRssFeed?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes['ImageAsset']>, ParentType, ContextType>;
  isDeleted?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  path?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  privacyPolicyEnabled?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  profileType?: Resolver<ResolversTypes['UserProfileType'], ParentType, ContextType>;
  spotifyShowId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  subscribable?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  subscribeRequestState?: Resolver<Maybe<ResolversTypes['SubscribeRequestState']>, ParentType, ContextType>;
  subscriber?: Resolver<Maybe<ResolversTypes['UserSubscriptionDetails']>, ParentType, ContextType>;
  subscription?: Resolver<Maybe<ResolversTypes['UserSubscriptionDetails']>, ParentType, ContextType>;
  tier?: Resolver<ResolversTypes['Tier'], ParentType, ContextType>;
  verified?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserAnalyticsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserAnalytics'] = ResolversParentTypes['UserAnalytics']> = {
  facebookPixelId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  ga4Stream?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  googleAdsConversionId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  googleAdsConversionLabel?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  leadHub?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  tiktokPixelId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserCompanyResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserCompany'] = ResolversParentTypes['UserCompany']> = {
  additionalInfo?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  address?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  city?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  country?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  iban?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  postalCode?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  registeredWith?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  swift?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  vatId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  vatRate?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  vatType?: Resolver<Maybe<ResolversTypes['UserCompanyVatType']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserConnectionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserConnection'] = ResolversParentTypes['UserConnection']> = {
  nodes?: Resolver<Array<ResolversTypes['User']>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserCountsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserCounts'] = ResolversParentTypes['UserCounts']> = {
  ownedCommunities?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  posts?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  supporters?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  supportersThreshold?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  supporting?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserDetailsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserDetails'] = ResolversParentTypes['UserDetails']> = {
  bio?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  bioMarkdown?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  categories?: Resolver<Array<ResolversTypes['Category']>, ParentType, ContextType>;
  counts?: Resolver<ResolversTypes['UserDetailsCounts'], ParentType, ContextType>;
  creator?: Resolver<Maybe<ResolversTypes['CreatorDetails']>, ParentType, ContextType>;
  discord?: Resolver<Maybe<ResolversTypes['UserDiscordSettings']>, ParentType, ContextType>;
  email?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  emailInvoice?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  emailPublic?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  hasPostPreviews?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  hasRssFeed?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  hasWelcomeMessageEnabled?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes['ImageAsset']>, ParentType, ContextType>;
  isOfAge?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  language?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  livestream?: Resolver<Maybe<ResolversTypes['LivestreamDetails']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  path?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  pathEditableAfter?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  profileType?: Resolver<ResolversTypes['UserProfileType'], ParentType, ContextType>;
  role?: Resolver<ResolversTypes['UserRole'], ParentType, ContextType>;
  spotify?: Resolver<Maybe<ResolversTypes['UserSpotifyDetails']>, ParentType, ContextType>;
  subscribable?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  tier?: Resolver<ResolversTypes['Tier'], ParentType, ContextType>;
  verified?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserDetailsCountsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserDetailsCounts'] = ResolversParentTypes['UserDetailsCounts']> = {
  incomes?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  incomesClean?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  invoices?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  payments?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  pendingRequests?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  posts?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  supporters?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  supporting?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserDetailsUpdateErrorResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserDetailsUpdateError'] = ResolversParentTypes['UserDetailsUpdateError']> = {
  errorType?: Resolver<ResolversTypes['UserDetailsUpdateErrorType'], ParentType, ContextType>;
  property?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  value?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserDetailsUpdatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserDetailsUpdatePayload'] = ResolversParentTypes['UserDetailsUpdatePayload']> = {
  errors?: Resolver<Maybe<Array<Maybe<ResolversTypes['UserDetailsUpdateError']>>>, ParentType, ContextType>;
  userDetails?: Resolver<Maybe<ResolversTypes['UserDetails']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserDiscordSettingsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserDiscordSettings'] = ResolversParentTypes['UserDiscordSettings']> = {
  guildId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserSpotifyDetailsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserSpotifyDetails'] = ResolversParentTypes['UserSpotifyDetails']> = {
  isConnected?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  podcastUri?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserSubscriptionResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserSubscription'] = ResolversParentTypes['UserSubscription']> = {
  __resolveType: TypeResolveFn<'UserSubscriptionDetails' | 'UserSubscriptionInfo', ParentType, ContextType>;
  creator?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  subscribedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  subscriber?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
};

export type UserSubscriptionDetailsResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserSubscriptionDetails'] = ResolversParentTypes['UserSubscriptionDetails']> = {
  applePaidPrice?: Resolver<Maybe<ResolversTypes['Price']>, ParentType, ContextType>;
  cancelAtPeriodEnd?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  couponAppliedForDays?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  couponAppliedForMonths?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  couponExpiresAt?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  couponMethod?: Resolver<Maybe<ResolversTypes['SubscriptionCouponMethod']>, ParentType, ContextType>;
  couponPercentOff?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  creator?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  expires?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  isAppleSubscription?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  status?: Resolver<ResolversTypes['SubscriptionStatus'], ParentType, ContextType>;
  subscribedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  subscriber?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  tier?: Resolver<Maybe<ResolversTypes['Tier']>, ParentType, ContextType>;
  type?: Resolver<ResolversTypes['SubscriptionType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserSubscriptionInfoResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['UserSubscriptionInfo'] = ResolversParentTypes['UserSubscriptionInfo']> = {
  creator?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  subscribedAt?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  subscriber?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface VoidScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Void'], any> {
  name: 'Void';
}

export type WelcomeMessageResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['WelcomeMessage'] = ResolversParentTypes['WelcomeMessage']> = {
  assets?: Resolver<Array<ResolversTypes['MessageAsset']>, ParentType, ContextType>;
  text?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type WelcomeMessageUpdatePayloadResolvers<ContextType = DataSourceContext, ParentType extends ResolversParentTypes['WelcomeMessageUpdatePayload'] = ResolversParentTypes['WelcomeMessageUpdatePayload']> = {
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  welcomeMessage?: Resolver<Maybe<ResolversTypes['WelcomeMessage']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type Resolvers<ContextType = DataSourceContext> = {
  BigInt?: GraphQLScalarType;
  Category?: CategoryResolvers<ContextType>;
  CategoryPayload?: CategoryPayloadResolvers<ContextType>;
  Comment?: CommentResolvers<ContextType>;
  CommentConnection?: CommentConnectionResolvers<ContextType>;
  CommentCreatePayload?: CommentCreatePayloadResolvers<ContextType>;
  CommentNotification?: CommentNotificationResolvers<ContextType>;
  CommentParent?: CommentParentResolvers<ContextType>;
  CommentUpdatePayload?: CommentUpdatePayloadResolvers<ContextType>;
  Community?: CommunityResolvers<ContextType>;
  CommunityCommentNotification?: CommunityCommentNotificationResolvers<ContextType>;
  CommunityConnection?: CommunityConnectionResolvers<ContextType>;
  CommunityCreatePayload?: CommunityCreatePayloadResolvers<ContextType>;
  CommunityThreadNotification?: CommunityThreadNotificationResolvers<ContextType>;
  CommunityUpdatePayload?: CommunityUpdatePayloadResolvers<ContextType>;
  CompleteContentPost?: CompleteContentPostResolvers<ContextType>;
  CreatorDetails?: CreatorDetailsResolvers<ContextType>;
  DateTime?: GraphQLScalarType;
  ExpectedIncome?: ExpectedIncomeResolvers<ContextType>;
  GenericMutationPayload?: GenericMutationPayloadResolvers<ContextType>;
  GenericNotification?: GenericNotificationResolvers<ContextType>;
  GjirafaAssetQuality?: GjirafaAssetQualityResolvers<ContextType>;
  ImageAsset?: ImageAssetResolvers<ContextType>;
  LimitedContentPost?: LimitedContentPostResolvers<ContextType>;
  LivestreamDetails?: LivestreamDetailsResolvers<ContextType>;
  Message?: MessageResolvers<ContextType>;
  MessageAsset?: MessageAssetResolvers<ContextType>;
  MessageConnection?: MessageConnectionResolvers<ContextType>;
  MessageCreatePayload?: MessageCreatePayloadResolvers<ContextType>;
  MessageLockedAsset?: MessageLockedAssetResolvers<ContextType>;
  MessageNotification?: MessageNotificationResolvers<ContextType>;
  MessageThread?: MessageThreadResolvers<ContextType>;
  MessageThreadConnection?: MessageThreadConnectionResolvers<ContextType>;
  MessageThreadUpdatePayload?: MessageThreadUpdatePayloadResolvers<ContextType>;
  MessageThreadUpsertPayload?: MessageThreadUpsertPayloadResolvers<ContextType>;
  Mutation?: MutationResolvers<ContextType>;
  Notification?: NotificationResolvers<ContextType>;
  NotificationConnection?: NotificationConnectionResolvers<ContextType>;
  NotificationSettings?: NotificationSettingsResolvers<ContextType>;
  NotificationSettingsUpdatePayload?: NotificationSettingsUpdatePayloadResolvers<ContextType>;
  PageInfo?: PageInfoResolvers<ContextType>;
  Poll?: PollResolvers<ContextType>;
  PollOption?: PollOptionResolvers<ContextType>;
  Post?: PostResolvers<ContextType>;
  PostAddToLibraryPayload?: PostAddToLibraryPayloadResolvers<ContextType>;
  PostAsset?: PostAssetResolvers<ContextType>;
  PostBunnyAsset?: PostBunnyAssetResolvers<ContextType>;
  PostConnection?: PostConnectionResolvers<ContextType>;
  PostCounts?: PostCountsResolvers<ContextType>;
  PostCreatePayload?: PostCreatePayloadResolvers<ContextType>;
  PostDocumentAsset?: PostDocumentAssetResolvers<ContextType>;
  PostEmptyAsset?: PostEmptyAssetResolvers<ContextType>;
  PostGjirafaAsset?: PostGjirafaAssetResolvers<ContextType>;
  PostGjirafaLivestreamAsset?: PostGjirafaLivestreamAssetResolvers<ContextType>;
  PostImageAsset?: PostImageAssetResolvers<ContextType>;
  PostNotification?: PostNotificationResolvers<ContextType>;
  PostPreviewAsset?: PostPreviewAssetResolvers<ContextType>;
  PostPreviewDocumentAsset?: PostPreviewDocumentAssetResolvers<ContextType>;
  PostPreviewGjirafaAsset?: PostPreviewGjirafaAssetResolvers<ContextType>;
  PostPreviewGjirafaLiveAsset?: PostPreviewGjirafaLiveAssetResolvers<ContextType>;
  PostPreviewImageAsset?: PostPreviewImageAssetResolvers<ContextType>;
  PostUpdatePayload?: PostUpdatePayloadResolvers<ContextType>;
  PostYoutubeAsset?: PostYoutubeAssetResolvers<ContextType>;
  PreviewContentPost?: PreviewContentPostResolvers<ContextType>;
  Price?: PriceResolvers<ContextType>;
  Query?: QueryResolvers<ContextType>;
  RandomizedFeaturedCreatorsPayload?: RandomizedFeaturedCreatorsPayloadResolvers<ContextType>;
  RequestNotification?: RequestNotificationResolvers<ContextType>;
  RssFeedUrl?: RssFeedUrlResolvers<ContextType>;
  RssFeedUrlGeneratePayload?: RssFeedUrlGeneratePayloadResolvers<ContextType>;
  SavedPost?: SavedPostResolvers<ContextType>;
  SavedPostConnection?: SavedPostConnectionResolvers<ContextType>;
  SavedPostInfo?: SavedPostInfoResolvers<ContextType>;
  StripeRequirements?: StripeRequirementsResolvers<ContextType>;
  SubscribeRequest?: SubscribeRequestResolvers<ContextType>;
  SubscribeRequestConnection?: SubscribeRequestConnectionResolvers<ContextType>;
  SubscriberNotification?: SubscriberNotificationResolvers<ContextType>;
  SubscriptionConnection?: SubscriptionConnectionResolvers<ContextType>;
  SubscriptionDeletePayload?: SubscriptionDeletePayloadResolvers<ContextType>;
  SubscriptionNotification?: SubscriptionNotificationResolvers<ContextType>;
  SubscriptionRenewPayload?: SubscriptionRenewPayloadResolvers<ContextType>;
  Tier?: TierResolvers<ContextType>;
  User?: UserResolvers<ContextType>;
  UserAnalytics?: UserAnalyticsResolvers<ContextType>;
  UserCompany?: UserCompanyResolvers<ContextType>;
  UserConnection?: UserConnectionResolvers<ContextType>;
  UserCounts?: UserCountsResolvers<ContextType>;
  UserDetails?: UserDetailsResolvers<ContextType>;
  UserDetailsCounts?: UserDetailsCountsResolvers<ContextType>;
  UserDetailsUpdateError?: UserDetailsUpdateErrorResolvers<ContextType>;
  UserDetailsUpdatePayload?: UserDetailsUpdatePayloadResolvers<ContextType>;
  UserDiscordSettings?: UserDiscordSettingsResolvers<ContextType>;
  UserSpotifyDetails?: UserSpotifyDetailsResolvers<ContextType>;
  UserSubscription?: UserSubscriptionResolvers<ContextType>;
  UserSubscriptionDetails?: UserSubscriptionDetailsResolvers<ContextType>;
  UserSubscriptionInfo?: UserSubscriptionInfoResolvers<ContextType>;
  Void?: GraphQLScalarType;
  WelcomeMessage?: WelcomeMessageResolvers<ContextType>;
  WelcomeMessageUpdatePayload?: WelcomeMessageUpdatePayloadResolvers<ContextType>;
};

