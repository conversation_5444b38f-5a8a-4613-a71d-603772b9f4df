import {
    CompleteContentPostResolvers,
    LimitedContentPostResolvers,
    PostAssetResolvers,
    PostGjirafaAssetResolvers,
    PostGjirafaLivestreamAssetResolvers,
    PostPreviewAssetResolvers,
    PostResolvers,
    PostSortFields,
    PostVoteType,
    PreviewContentPostResolvers,
} from '../generated/resolvers-types'
import { turndownService } from '../common/util'
import { unauthorizedError } from './utils'

export const completeContentPostResolver: CompleteContentPostResolvers = {
    comments: async ({ id }, { first, sortDirection }, { dataSources }) => {
        const { comments, pagination } = await dataSources.postAPI.getComments(id, { first, sortDirection })
        return {
            nodes: comments,
            pageInfo: pagination,
        }
    },
    markdown: async ({ textHtml }) => {
        if (!textHtml) return null

        return turndownService.turndown(textHtml)
    },
    poll: async ({ pollId }, _, { dataSources }) => {
        if (!pollId) {
            return null
        }

        return dataSources.postAPI.getPoll(pollId)
    },

    myVote: ({ myVote }) => {
        switch (myVote) {
            case 1:
                return PostVoteType.UP
            case 0:
                return PostVoteType.NONE
            case -1:
                return PostVoteType.DOWN
            default:
                throw new Error(`Unknown vote ${myVote}`)
        }
    },

    community: async ({ communityId }, _, { dataSources }) => {
        if (!communityId) {
            return null
        }

        return dataSources.communityAPI.getCommunity({ communityId })
    },
}

export const limitedContentPostResolvers: LimitedContentPostResolvers = {}

export const previewContentPostResolvers: PreviewContentPostResolvers = {
    poll: async ({ pollId }, _, { dataSources }) => {
        if (!pollId) {
            return null
        }

        return dataSources.postAPI.getPoll(pollId)
    },

    assets: ({ previewAssets }) => {
        return previewAssets
    },
}

export const postResolvers: PostResolvers = {
    __resolveType: ({ fullAssets, hasPreview }) => {
        if (fullAssets) {
            return 'CompleteContentPost'
        } else if (hasPreview) {
            return 'PreviewContentPost'
        } else {
            return 'LimitedContentPost'
        }
    },
    user: async ({ userId }, _, { dataSources }) => {
        return dataSources.userAPI.getUser(userId)
    },

    posts: async ({ userId, publishedAt }, { first, direction, reverse }, { dataSources }) => {
        const cursor = {
            lastPublishedAt: publishedAt,
            '@type': 'GetCreatorPostsPublishedAtCursor',
        }
        const { posts, pagination } = await dataSources.postAPI.getPosts(
            userId,
            {
                first,
                after: Buffer.from(JSON.stringify(cursor)).toString('base64'),
            },
            { by: PostSortFields.PUBLISHED_AT, order: direction },
            {}
        )

        if (reverse) {
            posts.reverse()
        }
        return {
            nodes: posts,
            pageInfo: pagination,
        }
    },
}

export const postPreviewAssetResolvers: PostPreviewAssetResolvers = {
    __resolveType: ({ assetType }) => {
        switch (assetType) {
            case 'preview-image':
                return 'PostPreviewImageAsset'
            case 'preview-gjirafa':
                return 'PostPreviewGjirafaAsset'
            case 'preview-document':
                return 'PostPreviewDocumentAsset'
            case 'preview-gjirafa-live':
                return 'PostPreviewGjirafaLiveAsset'
        }
    },
}

export const postAssetResolvers: PostAssetResolvers = {
    __resolveType: ({ assetType }) => {
        switch (assetType) {
            case 'document':
                return 'PostDocumentAsset'
            case 'gjirafa':
                return 'PostGjirafaAsset'
            case 'gjirafa-livestream':
                return 'PostGjirafaLivestreamAsset'
            case 'image':
                return 'PostImageAsset'
            case 'youtube':
                return 'PostYoutubeAsset'
            case 'bunny':
                return 'PostBunnyAsset'
            case 'empty':
                return 'PostEmptyAsset'
        }
    },
}

export const postGjirafaAssetResolvers: PostGjirafaAssetResolvers = {
    // eslint-disable-next-line @typescript-eslint/no-deprecated
    gjirafaId: ({ id }) => {
        return id
    },
}

export const postGjirafaLivestreamAssetResolvers: PostGjirafaLivestreamAssetResolvers = {
    // eslint-disable-next-line @typescript-eslint/no-deprecated
    gjirafaId: ({ id }) => {
        return id
    },

    // eslint-disable-next-line @typescript-eslint/no-deprecated
    startDateUTC: ({ id }, _, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        return dataSources.mediaAPI.getStartDateUTC(id.trim())
    },
}
