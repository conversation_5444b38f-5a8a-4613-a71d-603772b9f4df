CREATE TABLE apple_charge
(
    apple_transaction_id TEXT NOT NULL,
    appleReferenceId: String,
    createdAt: Instant,
    refundedAt: Instant?,
    userId: String,
    creatorId: String,
    tierId: String,
    targetAccountId: String,
    currencyStore: Currency,
    storefront: String?,
    stripe transfer id to distinguish the transfer has been made */
    stripeTransferId: String?,
    stripe reversal id to distinguish it was done */
    stripeTransferReversalId: String?,
    timestamp of the stripe transfer */
    transferredAt: Instant?,
    timestamp of the stripe transfer reversal */
    transferReversedAt: Instant?,
    conversion rate between store currency and tier in cents */
    conversionRateCents: Long?,
    price paid by user in store currency */
    priceStoreCents: Long,
    expected Herohero fee in store currency */
    priceFeeHeroheroCents: Long,
    amount to be transferred to creator's Stripe account in tier currency */
    transferCents: Long,
    stripe customer holding this subscription */
    customerId: String,
    should replicate behaviour of <PERSON><PERSON>'s `charge.description` */
    description: String,
)